import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { CommissionService } from '@shared/shared/modules/commission/commission.service';
import {
  CreateCommissionDto,
  UpdateCommissionDto,
  CommissionResponseDto,
  CommissionPaginationDto,
  CommissionPaginatedResponseDto,
} from './dto';
import {
  ApiResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('Commissions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('commissions')
export class CommissionsController {
  constructor(private readonly commissionService: CommissionService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new commission' })
  @ApiResponse({
    status: 201,
    description: 'Commission created successfully',
    type: ApiResponseDto<CommissionResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or duplicate name',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async createCommission(
    @Body() createCommissionDto: CreateCommissionDto,
  ): Promise<ApiResponseDto<CommissionResponseDto>> {
    const commission =
      await this.commissionService.createCommission(createCommissionDto);
    return {
      success: true,
      message: 'Commission created successfully',
      data: commission as unknown as CommissionResponseDto,
      timestamp: Date.now(),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get paginated commissions' })
  @ApiResponse({
    status: 200,
    description: 'Commissions retrieved successfully',
    type: CommissionPaginatedResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async getCommissions(
    @Query() paginationDto: CommissionPaginationDto,
  ): Promise<CommissionPaginatedResponseDto> {
    const result =
      await this.commissionService.paginateCommissions(paginationDto);
    return {
      success: true,
      message: 'Commissions retrieved successfully',
      data: result as unknown as {
        data: CommissionResponseDto[];
        meta: {
          page: number;
          limit: number;
          total: number;
          totalPages: number;
          hasNextPage: boolean;
          hasPrevPage: boolean;
        };
      },
      timestamp: Date.now(),
    };
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all commissions without pagination' })
  @ApiResponse({
    status: 200,
    description: 'All commissions retrieved successfully',
    type: ApiResponseDto<CommissionResponseDto[]>,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async getAllCommissions(): Promise<ApiResponseDto<CommissionResponseDto[]>> {
    const commissions = await this.commissionService.findAllCommissions();
    return {
      success: true,
      message: 'All commissions retrieved successfully',
      data: commissions as unknown as CommissionResponseDto[],
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get commission by ID' })
  @ApiParam({
    name: 'id',
    description: 'Commission ID',
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Commission retrieved successfully',
    type: ApiResponseDto<CommissionResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'Commission not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async getCommissionById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<CommissionResponseDto>> {
    const commission = await this.commissionService.findCommissionById(id);
    return {
      success: true,
      message: 'Commission retrieved successfully',
      data: commission as unknown as CommissionResponseDto,
      timestamp: Date.now(),
    };
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update commission by ID' })
  @ApiParam({
    name: 'id',
    description: 'Commission ID',
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Commission updated successfully',
    type: ApiResponseDto<CommissionResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or duplicate name',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Commission not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async updateCommission(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCommissionDto: UpdateCommissionDto,
  ): Promise<ApiResponseDto<CommissionResponseDto>> {
    const commission = await this.commissionService.updateCommission(
      id,
      updateCommissionDto,
    );
    return {
      success: true,
      message: 'Commission updated successfully',
      data: commission as unknown as CommissionResponseDto,
      timestamp: Date.now(),
    };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Delete commission by ID' })
  @ApiParam({
    name: 'id',
    description: 'Commission ID',
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Commission deleted successfully',
    type: ApiResponseDto<CommissionResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Commission is in use and cannot be deleted',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Commission not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async deleteCommission(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<CommissionResponseDto>> {
    const commission = await this.commissionService.deleteCommission(id);
    return {
      success: true,
      message: 'Commission deleted successfully',
      data: commission as unknown as CommissionResponseDto,
      timestamp: Date.now(),
    };
  }
}
