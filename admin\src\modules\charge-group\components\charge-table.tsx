'use client';

import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useDetachCharge } from '../api/charge-mutations';
import { Charge, CHARGE_TYPE_LABELS, PRICE_MODEL_LABELS } from '@/modules/charge/types/charge';
import { ChargeModal } from '@/modules/charge/components/charge-modal';
import { ChargeDeleteModal } from './charge-delete-modal';
import { AttachCommissionModal } from '@/modules/charge/components/add-commission-modal';
import { CommissionDetachModal } from '@/modules/charge/components/commission-detach-modal';
import { useDetachCommission } from '@/modules/charge/api/charge-commission-mutations';
// import { EditPriorityModal } from './edit-priority-modal';
import { Spinner } from '@/components/ui/spinner';
import { Info } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

const getColumns = ({
   detachChargeMutation,
   handleEditClick,
   handleDeleteClick,
   handleAttachCommissionClick,
   handleDetachCommissionClick,
   // handleEditPriorityClick,
   chargeToDelete,
}: {
   handleEditClick: (charge: Charge) => void;
   handleDeleteClick: (charge: Charge) => void;
   handleAttachCommissionClick: (charge: Charge) => void;
   handleDetachCommissionClick: (charge: Charge) => void;
   // handleEditPriorityClick: (charge: Charge) => void;
   detachChargeMutation: any;
   chargeToDelete: Charge | null;
}): ColumnDef<Charge>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left max-w-[160px]'>
               <div className='text-sm font-medium break-words'>{charge.name}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'identifier',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Identifier</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left max-w-[120px]'>
               <div className='text-sm text-gray-600 break-words'>{charge.identifier || '-'}</div>
            </div>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'chargeType',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Charge Type</div>
      ),
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>{CHARGE_TYPE_LABELS[charge.chargeType]}</div>
            </div>
         );
      },
      size: 100,
   },
   // {
   //   accessorKey: 'meter',
   //   header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Meter</div>,
   //   cell: ({ row }) => {
   //     const charge = row.original as Charge;
   //     return (
   //       <div className='text-left max-w-[150px]'>
   //         <div className='text-sm text-gray-600'>
   //           {charge.meter ? CHARGE_METER_LABELS[charge.meter] : '-'}
   //         </div>
   //       </div>
   //     );
   //   },
   //   size: 150,
   // },
   {
      accessorKey: 'priceModel',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Price Model</div>
      ),
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left max-w-[110px]'>
               <div className='text-sm text-gray-600'>{PRICE_MODEL_LABELS[charge.priceModel]}</div>
            </div>
         );
      },
      size: 110,
   },
   {
      accessorKey: 'price',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Price</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         const { price } = charge;

         // Display price based on price model
         let priceDisplay = '-';
         if (price) {
            if (price.amount !== undefined) {
               priceDisplay = `${price.currency || ''} ${price.amount}`.trim();
            } else if (price.rate !== undefined) {
               priceDisplay = `${price.currency || ''} ${price.rate}/unit`.trim();
            } else if (price.tiers && price.tiers.length > 0) {
               priceDisplay = `${price.tiers.length} tier(s)`;
            } else if (price.formula) {
               priceDisplay = 'Formula';
            }
         }

         if (charge.percentage !== undefined && charge.percentage !== null) {
            priceDisplay = `${charge.percentage}%`;
         }

         return (
            <div className='text-left max-w-[90px]'>
               <div className='text-sm text-gray-600'>{priceDisplay}</div>
            </div>
         );
      },
      size: 90,
   },
   {
      accessorKey: 'type',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm flex items-center gap-1'>
            Type
            <Tooltip>
               <TooltipTrigger asChild>
                  <Info className='h-3.5 w-3.5 text-gray-400 cursor-help' />
               </TooltipTrigger>
               <TooltipContent
                  side='top'
                  className='max-w-xs bg-white text-gray-900 border border-gray-200 shadow-lg'
                  arrowClassName='bg-white fill-white'
               >
                  <div className='space-y-1'>
                     <div>
                        <strong>Shared:</strong> Centrally managed charges attached to this group.
                        Edit them from the Charges tab.
                     </div>
                     <div>
                        <strong>Custom:</strong> Charges created specifically for this group only.
                     </div>
                  </div>
               </TooltipContent>
            </Tooltip>
         </div>
      ),
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left'>
               {charge.isCommon ? (
                  <Tooltip>
                     <TooltipTrigger asChild>
                        <span className='inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 cursor-help'>
                           Shared
                           <Info className='h-3 w-3' />
                        </span>
                     </TooltipTrigger>
                     <TooltipContent
                        side='top'
                        className='bg-white text-gray-900 border border-gray-200 shadow-lg'
                        arrowClassName='bg-white fill-white'
                     >
                        Centrally managed charges attached to this group. Edit them from the Charges
                        tab.
                     </TooltipContent>
                  </Tooltip>
               ) : (
                  <Tooltip>
                     <TooltipTrigger asChild>
                        <span className='inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 cursor-help'>
                           Custom
                           <Info className='h-3 w-3' />
                        </span>
                     </TooltipTrigger>
                     <TooltipContent
                        side='top'
                        className='bg-white text-gray-900 border border-gray-200 shadow-lg'
                        arrowClassName='bg-white fill-white'
                     >
                        Charges created specifically for this group only.
                     </TooltipContent>
                  </Tooltip>
               )}
            </div>
         );
      },
      size: 110,
   },
   // {
   //    accessorKey: 'priority',
   //    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Priority</div>,
   //    cell: ({ row }) => {
   //       const charge = row.original as Charge;
   //       return (
   //          <div className='text-left'>
   //             <div className='text-sm text-gray-600'>{charge.priority ?? '-'}</div>
   //          </div>
   //       );
   //    },
   //    size: 10,
   // },
   {
      accessorKey: 'commission',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Commission</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         console.log({ charge });
         return (
            <div className='text-left max-w-[130px]'>
               {charge.commission ? (
                  <div className='flex flex-col'>
                     <span className='text-sm text-gray-900 font-medium truncate'>
                        {charge.commission.name}
                     </span>
                     <span className='text-xs text-gray-500'>
                        {charge.commission.type === 'percentage'
                           ? `${charge.commission.percentageValue}%`
                           : charge.commission.flatValue}
                     </span>
                  </div>
               ) : (
                  <span className='text-sm text-gray-400'>-</span>
               )}
            </div>
         );
      },
      size: 100,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         const isDeleting = chargeToDelete?.id === charge.id && detachChargeMutation.isPending;

         return (
            <div className='flex justify-center gap-1'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-2 py-1 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                  onClick={() => handleEditClick(charge)}
                  disabled={isDeleting}
               >
                  Edit
               </button>
               {/* Commission buttons */}
               {charge.commission ? (
                  <button
                     className='text-sm font-medium text-orange-600 hover:text-orange-700 border border-orange-300 hover:border-orange-400 px-2 py-1 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                     onClick={() => handleDetachCommissionClick(charge)}
                     disabled={isDeleting}
                  >
                     Remove Comm.
                  </button>
               ) : (
                  <button
                     className='text-sm font-medium text-green-600 hover:text-green-700 border border-green-300 hover:border-green-400 px-2 py-1 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                     onClick={() => handleAttachCommissionClick(charge)}
                     disabled={isDeleting}
                  >
                     Add Comm.
                  </button>
               )}
               {/* <button
                  className='text-sm font-medium text-purple-600 hover:text-purple-700 border border-purple-300 hover:border-purple-400 px-2 py-1 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                  onClick={() => handleEditPriorityClick(charge)}
                  disabled={isDeleting}
               >
                  Priority
               </button> */}
               <button
                  className='text-sm font-medium text-red-600 hover:text-red-700 border border-red-300 hover:border-red-400 px-2 py-1 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                  onClick={() => handleDeleteClick(charge)}
                  disabled={isDeleting}
               >
                  {isDeleting ? '...' : 'Delete'}
               </button>
            </div>
         );
      },
      size: 250,
   },
];

interface ChargeTableProps {
   chargeGroupId: string;
   data: Charge[] | undefined;
   isLoading: boolean;
}

export function ChargeTable({ chargeGroupId, data, isLoading }: ChargeTableProps) {
   const [chargeToEdit, setChargeToEdit] = useState<Charge | null>(null);
   const [chargeToDelete, setChargeToDelete] = useState<Charge | null>(null);
   const [chargeForCommission, setChargeForCommission] = useState<Charge | null>(null);
   const [chargeToDetachCommission, setChargeToDetachCommission] = useState<Charge | null>(null);
   // const [chargeToEditPriority, setChargeToEditPriority] = useState<Charge | null>(null);
   const detachChargeMutation = useDetachCharge();
   const detachCommissionMutation = useDetachCommission();
   const queryClient = useQueryClient();

   const handleEditClick = (charge: Charge) => {
      if (charge.isCommon) {
         toast.info('Shared charges can only be edited from the Charges tab');
         return;
      }
      setChargeToEdit(charge);
   };

   const handleDeleteClick = (charge: Charge) => {
      setChargeToDelete(charge);
   };

   const handleAttachCommissionClick = (charge: Charge) => {
      setChargeForCommission(charge);
   };

   const handleDetachCommissionClick = (charge: Charge) => {
      setChargeToDetachCommission(charge);
   };

   const handleDetachCommissionConfirm = () => {
      if (!chargeToDetachCommission?.commissionId) return;

      detachCommissionMutation.mutate(
         { chargeId: chargeToDetachCommission.id },
         {
            onSuccess: () => {
               toast.success('Commission detached successfully');
               queryClient.invalidateQueries({ queryKey: ['charges', chargeGroupId] });
            },
            onError: (error: any) => {
               const errorMessage = error?.response?.data?.message || 'Failed to detach commission';
               toast.error(errorMessage);
            },
            onSettled: () => {
               setChargeToDetachCommission(null);
            },
         }
      );
   };

   // const handleEditPriorityClick = (charge: Charge) => {
   //   setChargeToEditPriority(charge);
   // };

   const handleDeleteConfirm = () => {
      if (!chargeToDelete) return;

      detachChargeMutation.mutate(
         { chargeGroupId, chargeId: chargeToDelete.id },
         {
            onSuccess: () => {
               toast.success('Charge detached successfully');
               queryClient.invalidateQueries({ queryKey: ['charges', chargeGroupId] });
            },
            onSettled: () => {
               setChargeToDelete(null);
            },
         }
      );
   };

   const columns = getColumns({
      detachChargeMutation,
      handleEditClick,
      handleDeleteClick,
      handleAttachCommissionClick,
      handleDetachCommissionClick,
      // handleEditPriorityClick,
      chargeToDelete,
   });

   const table = useReactTable({
      data: data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return (
         <div className='flex items-center justify-center py-8'>
            <Spinner className='h-8 w-8' />
         </div>
      );
   }

   if (!data?.length) {
      return (
         <div className='text-center py-12 border rounded-md bg-gray-50'>
            <p className='text-gray-500 mb-4'>No charges found in this group</p>
            <p className='text-sm text-gray-400'>Click "Add Charge" to create your first charge</p>
         </div>
      );
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full table-fixed'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize(), maxWidth: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td
                                 key={cell.id}
                                 className='px-4 py-3 align-middle'
                                 style={{
                                    width: cell.column.getSize(),
                                    maxWidth: cell.column.getSize(),
                                 }}
                              >
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {/* Delete Confirmation Modal */}
         <ChargeDeleteModal
            isOpen={!!chargeToDelete}
            onClose={() => setChargeToDelete(null)}
            onConfirm={handleDeleteConfirm}
            isLoading={detachChargeMutation.isPending}
            chargeName={chargeToDelete?.name || ''}
         />

         {/* Edit Modal */}
         <ChargeModal
            mode='edit'
            chargeGroupId={chargeGroupId}
            chargeId={chargeToEdit?.id || null}
            chargeData={chargeToEdit || undefined}
            isOpen={!!chargeToEdit}
            onClose={() => setChargeToEdit(null)}
         />

         {/* Edit Priority Modal */}
         {/* <EditPriorityModal
        chargeGroupId={chargeGroupId}
        chargeGroupChargeId={chargeToEditPriority?.chargeGroupChargeId || null}
        currentPriority={chargeToEditPriority?.priority ?? 0}
        chargeName={chargeToEditPriority?.name || ''}
        isOpen={!!chargeToEditPriority}
        onClose={() => setChargeToEditPriority(null)}
      /> */}

         {/* Add Commission Modal */}
         {chargeForCommission && (
            <AttachCommissionModal
               chargeId={chargeForCommission.id}
               chargeName={chargeForCommission.name}
               isOpen={!!chargeForCommission}
               onClose={() => setChargeForCommission(null)}
            />
         )}

         {/* Detach Commission Confirmation Modal */}
         <CommissionDetachModal
            isOpen={!!chargeToDetachCommission}
            onClose={() => setChargeToDetachCommission(null)}
            onConfirm={handleDetachCommissionConfirm}
            isLoading={detachCommissionMutation.isPending}
            chargeName={chargeToDetachCommission?.name || ''}
            commissionName={chargeToDetachCommission?.commission?.name || ''}
         />
      </div>
   );
}