import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
  UnauthorizedException,
  forwardRef,
  Inject,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { UserRepository } from '../../repositories/user.repository';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { RoleRepository } from '../../repositories/role.repository';
import { AuthCredentialRepository } from '../../repositories/auth-credential.repository';
import { CityAdminRepository } from '../../repositories/city-admin.repository';
import { NotificationService } from '../../common/notifications/engagespot/engagespot.service';
import { AppConfigService } from '../../config/config.service';
import { UserProfileStatus } from '../../repositories/models/userProfile.model';
import { City } from '../../repositories/models/city.model';
import { RoleService } from '../role/role.service';
import { TokenService } from '../auth/services/token.service';
import { AuthTokensWithProfileDto } from 'apps/api/src/v1/auth/dto';
import { AuthProvider } from '@shared/shared/common/constants/constants';
import { CityAdmin } from '@shared/shared/repositories/models/cityAdmin.model';

export interface InviteAdminData {
  firstName: string;
  lastName: string;
  email: string;
  roleId?: string;
  identifier?: string;
  cityId?: string;
}

export interface MultiCityInviteAdminData {
  firstName: string;
  lastName: string;
  email: string;
  roleId?: string;
  identifier?: string;
  cityIds: string[];
}

export interface SetupPasswordData {
  token: string;
  password: string;
  passwordConfirmation: string;
}

export interface VerifyTokenData {
  token: string;
}

export interface InvitationTokenPayload {
  userProfileId: string;
  roleId: string;
  iat?: number;
  exp?: number;
}

export interface InviteAdminResponse {
  userProfile: any;
  user: any;
  role: any;
  city?: City;
  cityAdmin?: CityAdmin;
  inviteLink?: string;
}

export interface MultiCityInviteAdminResponse {
  userProfile: any;
  user: any;
  role: any;
  // cities: City[];
  cityAdmins: CityAdmin[];
  inviteLink?: string;
  message: string;
}

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly roleRepository: RoleRepository,
    private readonly roleService: RoleService,
    @Inject(forwardRef(() => AuthCredentialRepository))
    private readonly authCredentialRepository: AuthCredentialRepository,
    private readonly cityAdminRepository: CityAdminRepository,
    private readonly notificationService: NotificationService,
    private readonly configService: AppConfigService,
    private readonly jwtService: JwtService,
    private readonly tokenService: TokenService,
  ) {}

  /**
   * Invite a user to be an admin/sub-admin for a specific city
   */
  async inviteAdmin(data: InviteAdminData): Promise<InviteAdminResponse> {
    this.logger.log(`Inviting admin with email: ${data.email}`);

    // Validate role and get role entity
    const role = await this.validateAndGetRole(data.roleId, data.identifier);

    // Find or create user
    const { user, isExistingUser } = await this.findOrCreateUser(data.email);

    // Create user profile (don't allow existing profile for single city admin)
    const userProfile = await this.createOrUpdateUserProfile(
      user.id,
      role.id,
      { firstName: data.firstName, lastName: data.lastName },
      isExistingUser,
      false, // Don't allow existing profile
    );

    // Get complete profile with relations
    const profileWithUser = await this.userProfileRepository.findById(
      userProfile.id,
    );

    const responseData: InviteAdminResponse = {
      userProfile: profileWithUser,
      user,
      role,
    };

    // For existing users, return without invite link
    if (isExistingUser) {
      return responseData;
    }

    // Generate invitation token and link
    const { inviteLink } = this.generateInviteTokenAndLink(
      userProfile.id,
      role.id,
    );

    console.log(inviteLink);

    // Send invitation notification
    await this.sendInvitationNotification(
      userProfile,
      user,
      role,
      inviteLink,
      data,
    );

    responseData.inviteLink = inviteLink;
    return responseData;
  }

  /**
   * Invite a user to be an admin/sub-admin for multiple cities
   */
  async inviteMultiCityAdmin(
    data: MultiCityInviteAdminData,
  ): Promise<MultiCityInviteAdminResponse> {
    this.logger.log(
      `Inviting admin with email: ${data.email} for ${data.cityIds.length} cities`,
    );

    // Validate role and get role entity
    const role = await this.validateAndGetRole(data.roleId, data.identifier);

    // Find or create user
    const { user, isExistingUser } = await this.findOrCreateUser(data.email);

    // Create or update user profile (allow existing profile for multi-city admin)
    const userProfile = await this.createOrUpdateUserProfile(
      user.id,
      role.id,
      { firstName: data.firstName, lastName: data.lastName },
      isExistingUser,
      true, // Allow existing profile
    );

    // Create city admin assignments for all cities
    const cityAdmins: CityAdmin[] = [];
    for (const cityId of data.cityIds) {
      const cityAdmin = await this.cityAdminRepository.createCityAdmin(
        cityId,
        userProfile.id,
      );
      cityAdmins.push(cityAdmin);
      this.logger.log(
        `Assigned user profile ${userProfile.id} as admin for city ${cityId}`,
      );
    }

    const responseData: MultiCityInviteAdminResponse = {
      userProfile,
      user,
      role,
      cityAdmins,
      message: `Invitation sent successfully`,
    };

    // For existing users, return without invite link
    if (isExistingUser) {
      responseData.message = `User already exists. Selected cities have been assigned to the user.`;
      return responseData;
    }

    // Generate invitation token and link
    const { inviteLink } = this.generateInviteTokenAndLink(
      userProfile.id,
      role.id,
    );

    console.log(inviteLink);

    // Send invitation notification
    await this.sendInvitationNotification(
      userProfile,
      user,
      role,
      inviteLink,
      data,
    );

    responseData.inviteLink = inviteLink;
    return responseData;
  }

  /**
   * Verify invitation token and get profile details
   */
  async verifyInvitationToken(token: string): Promise<any> {
    this.logger.log('Verifying invitation token');

    // Verify and decode JWT token
    let tokenPayload: InvitationTokenPayload;
    try {
      tokenPayload = this.jwtService.verify(token, {
        secret: this.configService.jwtSecret,
      }) as InvitationTokenPayload;
    } catch (error) {
      const err = error as any;
      if (err.name === 'TokenExpiredError') {
        throw new UnauthorizedException('Invitation token has expired');
      } else if (err.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('Invalid invitation token');
      }
      throw new UnauthorizedException('Token verification failed');
    }

    // Get user profile
    const userProfile = await this.userProfileRepository.findById(
      tokenPayload.userProfileId,
    );
    if (!userProfile) {
      throw new NotFoundException('User profile not found');
    }
    const existingCredential = await this.userHasPasswordAuth(
      userProfile.userId,
    );

    if (existingCredential) {
      throw new UnauthorizedException('Invitation already accepted');
    }
    // Verify role matches
    if (userProfile.roleId !== tokenPayload.roleId) {
      throw new UnauthorizedException('Token role mismatch');
    }

    // Get user and role
    const user = await this.userRepository.findById(userProfile.userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const role = await this.roleRepository.findById(userProfile.roleId);

    return {
      ...userProfile,
      user,
      role,
    };
  }

  //private check if user has password authentication
  private async userHasPasswordAuth(userId: string): Promise<boolean> {
    let existingCredential =
      await this.authCredentialRepository.findByUserAndType(
        userId,
        'PASSWORD' as AuthProvider,
      );
    if (!existingCredential) {
      return false;
    }
    return true;
  }
  /**
   * Remove admin from city
   */
  async removeCityAdmin(
    cityId: string,
    userProfileId: string,
  ): Promise<boolean> {
    const removed = await this.cityAdminRepository.removeCityAdmin(
      cityId,
      userProfileId,
    );
    if (removed) {
      this.logger.log(`Removed admin ${userProfileId} from city ${cityId}`);
    }
    return !!removed;
  }

  /**
   * Get paginated list of city admins
   */
  async getCityAdminsPaginated(
    cityId: string,
    page: number = 1,
    limit: number = 10,
    search?: string,
    status?: string,
  ) {
    return this.cityAdminRepository.getCityAdminsPaginated(
      cityId,
      page,
      limit,
      search,
      status,
    );
  }

  /**
   * Setup password for invited admin
   */
  async setupPassword(
    data: SetupPasswordData,
  ): Promise<AuthTokensWithProfileDto> {
    this.logger.log('Setting up password for invited admin');

    // Validate password confirmation
    if (data.password !== data.passwordConfirmation) {
      throw new BadRequestException(
        'Password and password confirmation do not match',
      );
    }

    // Verify and decode JWT token
    let tokenPayload: InvitationTokenPayload;
    try {
      tokenPayload = this.jwtService.verify(data.token, {
        secret: this.configService.jwtSecret,
      }) as InvitationTokenPayload;
    } catch (error) {
      const err = error as any;
      if (err.name === 'TokenExpiredError') {
        throw new UnauthorizedException('Invitation token has expired');
      } else if (err.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('Invalid invitation token');
      }
      throw new UnauthorizedException('Token verification failed');
    }

    // Get user profile
    const userProfile = await this.userProfileRepository.findById(
      tokenPayload.userProfileId,
    );
    if (!userProfile) {
      throw new NotFoundException('User profile not found');
    }

    // Verify role matches
    if (userProfile.roleId !== tokenPayload.roleId) {
      throw new UnauthorizedException('Token role mismatch');
    }

    // Get user
    const user = await this.userRepository.findById(userProfile.userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Hash new password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(data.password, saltRounds);

    // Find existing password credential
    const existingCredential =
      await this.authCredentialRepository.findByUserAndType(
        user.id,
        'PASSWORD' as AuthProvider,
      );

    if (existingCredential) {
      // Update existing password
      await this.authCredentialRepository.updateById(existingCredential.id, {
        identifier: hashedPassword,
      });
    } else {
      // Create new password credential
      await this.authCredentialRepository.createWithUser(
        user.id,
        'PASSWORD' as AuthProvider,
        hashedPassword,
      );
    }

    // Update user profile status to active
    await this.userProfileRepository.updateUserProfileById(userProfile.id, {
      status: UserProfileStatus.ACTIVE,
    });

    // Get role for token generation
    const role = await this.roleRepository.findById(userProfile.roleId);
    if (!role) {
      throw new NotFoundException('Role not found');
    }

    // Generate authentication tokens
    const tokens = await this.tokenService.generateTokens(
      user.id,
      role,
      userProfile,
    );

    this.logger.log(
      `Password setup completed for user profile: ${userProfile.id}`,
    );

    return tokens;
  }

  /**
   * Get admin profile details by profile ID
   */
  async getAdminProfile(profileId: string): Promise<any> {
    this.logger.log(`Getting admin profile: ${profileId}`);

    const profile =
      await this.userProfileRepository.findWithAdminDetails(profileId);
    if (!profile) {
      throw new NotFoundException('Admin profile not found');
    }

    return profile;
  }

  /**
   * Resend invitation to an existing admin profile
   */
  async resendInvite(profileId: string): Promise<any> {
    this.logger.log(`Resending invitation for profile: ${profileId}`);

    // Get the admin profile
    const profile = await this.userProfileRepository.findById(profileId);
    if (!profile) {
      throw new NotFoundException('Admin profile not found');
    }

    // Check if profile is already active
    if (profile.status === UserProfileStatus.ACTIVE) {
      throw new BadRequestException('Admin profile is already active');
    }

    // Get associated user
    const user = await this.userRepository.findById(profile.userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get role
    const role = await this.roleRepository.findById(profile.roleId);
    if (!role) {
      throw new NotFoundException('Role not found');
    }

    // Generate new invitation token
    const invitationToken = this.generateInvitationToken(profile.id, role.id);

    // Generate invitation link
    const frontendUrl =
      this.configService.adminFrontendUrl || 'https://tukxi-admin.netlify.app';
    const inviteLink = `${frontendUrl}/invitations?token=${invitationToken}`;

    // Prepare notification data
    const notificationData = {
      name: `${profile.firstName} ${profile.lastName}`,
      email: user.email || '',
      inviteLink,
      // cityName: cityNames || 'N/A',
      roleName: role.name,
    };

    // Send invitation email via EngageSpot
    try {
      await this.notificationService.sendNotification(
        'invite_subadmin',
        [
          {
            identifier: profile.id,
            email: user.email || '',
            userType: 'ADMIN',
          },
        ],
        notificationData,
      );

      this.logger.log(`Invitation email resent to: ${user.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to resend invitation email: ${(error as Error).message}`,
      );
      throw new BadRequestException('Failed to send invitation email');
    }

    return {
      inviteLink,
      userProfile: profile,
      user,
      role,
    };
  }

  /**
   * Change admin profile status between active and inactive
   */
  async changeProfileStatus(
    profileId: string,
    newStatus: string,
  ): Promise<any> {
    this.logger.log(
      `Changing profile status for: ${profileId} to ${newStatus}`,
    );

    // Get the admin profile
    const profile = await this.userProfileRepository.findById(profileId);
    if (!profile) {
      throw new NotFoundException('Admin profile not found');
    }

    // Check if the profile is already in the requested status
    if (profile.status === newStatus) {
      throw new BadRequestException(`Profile is already ${newStatus}`);
    }

    const previousStatus = profile.status;

    // Update the profile status
    const updatedProfile = await this.userProfileRepository.updateById(
      profileId,
      {
        status: newStatus as any, // Cast to match the enum type
      },
    );

    this.logger.log(
      `Profile status changed from ${previousStatus} to ${newStatus} for profile: ${profileId}`,
    );

    return updatedProfile;
  }

  /**
   * Remove multiple city admins by hard deleting their entries
   */
  async removeCityAdmins(cityAdminIds: string[]): Promise<any> {
    this.logger.log(`Removing ${cityAdminIds.length} city admin entries`);

    const { count } =
      await this.cityAdminRepository.hardDeleteByIds(cityAdminIds);

    this.logger.log(
      `Successfully removed ${count} city admin entries out of ${cityAdminIds.length} requested`,
    );
  }
  /**
   * addAdminToCity
   *
   */

  async addAdminToCity(cityId: string, adminIds: string[]): Promise<any> {
    this.logger.log(`Adding ${adminIds.length} admins to city: ${cityId}`);
    adminIds.forEach(async (adminId) => {
      // Check if the user profile exists
      // const userProfile = await this.userProfileRepository.findById(adminId);
      // if (!userProfile) {
      //   throw new NotFoundException(`User profile not found for ID: ${adminId}`);
      // }
      // Create city admin assignment
      await this.cityAdminRepository.createCityAdmin(cityId, adminId);
    });
  }

  /**
   * Validate role input and return role entity
   */
  private async validateAndGetRole(roleId?: string, identifier?: string) {
    // Validate exactly one of roleId or identifier is provided
    if (!roleId && !identifier) {
      throw new BadRequestException('Either roleId or identifier is required');
    }
    if (roleId && identifier) {
      throw new BadRequestException(
        'Provide either roleId or identifier, not both',
      );
    }

    let role;
    if (roleId) {
      role = await this.roleRepository.findById(roleId);
    } else if (identifier) {
      role = await this.roleService.getRoleByIdentifier(identifier);
    }

    if (!role) {
      throw new NotFoundException(`Role not found`);
    }

    return role;
  }

  /**
   * Find existing user or create new one
   */
  private async findOrCreateUser(email: string) {
    let user = await this.userRepository.findByEmail(email);
    //if user has password authentication
    let isExistingUser = !!user;

    if (user) {
      const hasPasswordAuth = await this.userHasPasswordAuth(user?.id);
      isExistingUser = !!user || hasPasswordAuth;
      return { user, isExistingUser };
    }
    if (!user) {
      // Create new user
      user = await this.userRepository.create({
        email,
        phoneNumber: null,
        emailVerifiedAt: null,
        phoneVerifiedAt: null,
        isPolicyAllowed: false,
        otpSecret: null,
      });
      this.logger.log(`Created new user with ID: ${user.id}`);
    }

    return { user, isExistingUser };
  }

  /**
   * Create or update user profile
   */
  private async createOrUpdateUserProfile(
    userId: string,
    roleId: string,
    profileData: { firstName: string; lastName: string },
    isExistingUser: boolean,
    allowExistingProfile = false,
  ) {
    // Check if user profile already exists with the same role
    let existingProfile =
      await this.userProfileRepository.getOneByUserIdAndRoleId(userId, roleId);

    if (existingProfile && !allowExistingProfile) {
      throw new BadRequestException('User already has this admin role');
    }

    const profileStatus = isExistingUser
      ? UserProfileStatus.ACTIVE
      : UserProfileStatus.INVITED;

    let userProfile;
    if (existingProfile && allowExistingProfile) {
      // Update existing profile
      await this.userProfileRepository.updateUserProfileById(
        existingProfile.id,
        {
          status: profileStatus,
        },
      );
      userProfile = existingProfile;
      this.logger.log(
        `Updated existing user profile with ID: ${userProfile.id}`,
      );
    } else {
      // Create new profile
      userProfile = await this.userProfileRepository.createUserProfile(
        userId,
        roleId,
        {
          firstName: profileData.firstName,
          lastName: profileData.lastName,
          status: profileStatus,
        },
      );
      this.logger.log(`Created user profile with ID: ${userProfile.id}`);
    }

    return userProfile;
  }

  /**
   * Generate invitation token and link
   */
  private generateInviteTokenAndLink(userProfileId: string, roleId: string) {
    const invitationToken = this.generateInvitationToken(userProfileId, roleId);
    const frontendUrl =
      this.configService.adminFrontendUrl || 'https://tukxi-admin.netlify.app';
    const inviteLink = `${frontendUrl}/invitations?token=${invitationToken}`;

    return { invitationToken, inviteLink };
  }

  /**
   * Send invitation notification email
   */
  private async sendInvitationNotification(
    userProfile: any,
    user: any,
    role: any,
    inviteLink: string,
    profileData: { firstName: string; lastName: string; email: string },
    additionalData?: any,
  ) {
    const notificationData = {
      name: `${profileData.firstName} ${profileData.lastName}`,
      email: profileData.email,
      inviteLink,
      roleName: role.name,
      ...additionalData,
    };

    try {
      await this.notificationService.sendNotification(
        'invite_subadmin',
        [
          {
            identifier: userProfile.id,
            email: user.email || profileData.email,
            userType: 'ADMIN',
          },
        ],
        notificationData,
      );

      this.logger.log(`Invitation email sent to: ${profileData.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send invitation email: ${(error as Error).message}`,
      );
      // Don't throw error here, as the profile is already created
    }
  }

  /**
   * Generate invitation JWT token
   */
  private generateInvitationToken(
    userProfileId: string,
    roleId: string,
  ): string {
    const payload: InvitationTokenPayload = {
      userProfileId,
      roleId,
    };

    return this.jwtService.sign(payload, {
      secret: this.configService.jwtSecret,
      expiresIn: '1d',
    });
  }
}
