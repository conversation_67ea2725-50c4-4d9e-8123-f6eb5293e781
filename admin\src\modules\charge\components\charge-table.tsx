'use client';

import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useDeleteCharge } from '../api/charge-mutations';
import { Charge, CHARGE_TYPE_LABELS, PRICE_MODEL_LABELS } from '../types/charge';
import { ChargeModal } from './charge-modal';
import { ChargeDeleteModal } from './charge-delete-modal';
import { AttachCommissionModal } from './add-commission-modal';
import { CommissionDetachModal } from './commission-detach-modal';
import { useDetachCommission } from '../api/charge-commission-mutations';
import { Spinner } from '@/components/ui/spinner';
import {
   USE_ROLE_BASED_ACCESS,
   useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

const getColumns = ({
   deleteChargeMutation,
   handleEditClick,
   handleDeleteClick,
   handleAttachCommissionClick,
   handleDetachCommissionClick,
   chargeToDelete,
   withPermission,
}: {
   handleEditClick: (charge: Charge) => void;
   handleDeleteClick: (charge: Charge) => void;
   handleAttachCommissionClick: (charge: Charge) => void;
   handleDetachCommissionClick: (charge: Charge) => void;
   deleteChargeMutation: any;
   chargeToDelete: Charge | null;
   withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<Charge>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left max-w-[200px]'>
               <div className='text-sm font-medium break-words'>{charge.name}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'identifier',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Identifier</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left max-w-[150px]'>
               <div className='text-sm text-gray-600 break-words'>{charge.identifier || '-'}</div>
            </div>
         );
      },
      size: 160,
   },
   {
      accessorKey: 'chargeType',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Charge Type</div>
      ),
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>{CHARGE_TYPE_LABELS[charge.chargeType]}</div>
            </div>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'priceModel',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Price Model</div>
      ),
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left max-w-[150px]'>
               <div className='text-sm text-gray-600'>{PRICE_MODEL_LABELS[charge.priceModel]}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'price',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Price</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         const { price } = charge;

         // Display price based on price model
         let priceDisplay = '-';
         if (price) {
            if (price.amount !== undefined) {
               priceDisplay = `${price.currency || ''} ${price.amount}`.trim();
            } else if (price.rate !== undefined) {
               priceDisplay = `${price.currency || ''} ${price.rate}/unit`.trim();
            } else if (price.tiers && price.tiers.length > 0) {
               priceDisplay = `${price.tiers.length} tier(s)`;
            } else if (price.formula) {
               priceDisplay = 'Formula';
            }
         }

         if (charge.percentage !== undefined && charge.percentage !== null) {
            priceDisplay = `${charge.percentage}%`;
         }

         return (
            <div className='text-left max-w-[150px]'>
               <div className='text-sm text-gray-600'>{priceDisplay}</div>
            </div>
         );
      },
      size: 100,
   },
   {
      accessorKey: 'commission',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Commission</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         return (
            <div className='text-left max-w-[150px]'>
               {charge.commission ? (
                  <div className='flex flex-col'>
                     <span className='text-sm text-gray-900 font-medium truncate'>
                        {charge.commission.name}
                     </span>
                     <span className='text-xs text-gray-500'>
                        {charge.commission.type === 'percentage'
                           ? `${charge.commission.percentageValue}%`
                           : charge.commission.flatValue}
                     </span>
                  </div>
               ) : (
                  <span className='text-sm text-gray-400'>-</span>
               )}
            </div>
         );
      },
      size: 120,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const charge = row.original as Charge;
         const isDeleting = chargeToDelete?.id === charge.id && deleteChargeMutation.isPending;

         return (
            <div className='flex justify-center gap-1'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-2 py-1 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.CHARGE.EDIT, () => handleEditClick(charge));
                  }}
                  disabled={isDeleting}
               >
                  Edit
               </button>
               {/* Commission buttons */}
               {charge.commission ? (
                  <button
                     className='text-sm font-medium text-orange-600 hover:text-orange-700 border border-orange-300 hover:border-orange-400 px-2 py-1 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                     onClick={() => handleDetachCommissionClick(charge)}
                     disabled={isDeleting}
                  >
                     Remove Comm.
                  </button>
               ) : (
                  <button
                     className='text-sm font-medium text-green-600 hover:text-green-700 border border-green-300 hover:border-green-400 px-2 py-1 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                     onClick={() => handleAttachCommissionClick(charge)}
                     disabled={isDeleting}
                  >
                     Add Comm.
                  </button>
               )}
               <button
                  className='text-sm font-medium text-red-600 hover:text-red-700 border border-red-300 hover:border-red-400 px-2 py-1 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.CHARGE.DELETE, () =>
                        handleDeleteClick(charge)
                     );
                  }}
                  disabled={isDeleting}
               >
                  {isDeleting ? '...' : 'Delete'}
               </button>
            </div>
         );
      },
      size: 280,
   },
];

interface ChargeTableProps {
   data: Charge[] | undefined;
   isLoading: boolean;
}

export function ChargeTable({ data, isLoading }: ChargeTableProps) {
   const [chargeToEdit, setChargeToEdit] = useState<Charge | null>(null);
   const [chargeToDelete, setChargeToDelete] = useState<Charge | null>(null);
   const [chargeForCommission, setChargeForCommission] = useState<Charge | null>(null);
   const [chargeToDetachCommission, setChargeToDetachCommission] = useState<Charge | null>(null);
   const deleteChargeMutation = useDeleteCharge();
   const detachCommissionMutation = useDetachCommission();
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   const handleEditClick = (charge: Charge) => {
      setChargeToEdit(charge);
   };

   const handleDeleteClick = (charge: Charge) => {
      setChargeToDelete(charge);
   };

   const handleAttachCommissionClick = (charge: Charge) => {
      setChargeForCommission(charge);
   };

   const handleDetachCommissionClick = (charge: Charge) => {
      setChargeToDetachCommission(charge);
   };

   const handleDetachCommissionConfirm = () => {
      if (!chargeToDetachCommission?.commissionId) return;

      detachCommissionMutation.mutate(
         { chargeId: chargeToDetachCommission.id },
         {
            onSuccess: () => {
               toast.success('Commission detached successfully');
               queryClient.invalidateQueries({ queryKey: ['all-charges'] });
            },
            onError: (error: any) => {
               const errorMessage = error?.response?.data?.message || 'Failed to detach commission';
               toast.error(errorMessage);
            },
            onSettled: () => {
               setChargeToDetachCommission(null);
            },
         }
      );
   };

   const handleDeleteConfirm = () => {
      if (!chargeToDelete) return;

      deleteChargeMutation.mutate(chargeToDelete.id, {
         onSuccess: () => {
            toast.success('Charge deleted successfully');
            queryClient.invalidateQueries({ queryKey: ['all-charges'] });
         },
         onSettled: () => {
            setChargeToDelete(null);
         },
      });
   };

   const columns = getColumns({
      deleteChargeMutation,
      handleEditClick,
      handleDeleteClick,
      handleAttachCommissionClick,
      handleDetachCommissionClick,
      chargeToDelete,
      withPermission,
   });

   const table = useReactTable({
      data: data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return (
         <div className='flex items-center justify-center py-8'>
            <Spinner className='h-8 w-8' />
         </div>
      );
   }

   if (!data?.length) {
      return (
         <div className='text-center py-12 border rounded-md bg-gray-50'>
            <p className='text-gray-500 mb-4'>No charges found</p>
            <p className='text-sm text-gray-400'>
               Click "Add New Charge" to create your first charge
            </p>
         </div>
      );
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full table-fixed'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize(), maxWidth: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td
                                 key={cell.id}
                                 className='px-4 py-3 align-middle'
                                 style={{
                                    width: cell.column.getSize(),
                                    maxWidth: cell.column.getSize(),
                                 }}
                              >
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {/* Delete Confirmation Modal */}
         <ChargeDeleteModal
            isOpen={!!chargeToDelete}
            onClose={() => setChargeToDelete(null)}
            onConfirm={handleDeleteConfirm}
            isLoading={deleteChargeMutation.isPending}
            chargeName={chargeToDelete?.name || ''}
         />

         {/* Edit Modal */}
         <ChargeModal
            mode='edit'
            chargeId={chargeToEdit?.id || null}
            chargeData={chargeToEdit || undefined}
            isOpen={!!chargeToEdit}
            onClose={() => setChargeToEdit(null)}
         />

         {/* Add Commission Modal */}
         {chargeForCommission && (
            <AttachCommissionModal
               chargeId={chargeForCommission.id}
               chargeName={chargeForCommission.name}
               isOpen={!!chargeForCommission}
               onClose={() => setChargeForCommission(null)}
            />
         )}

         {/* Detach Commission Confirmation Modal */}
         <CommissionDetachModal
            isOpen={!!chargeToDetachCommission}
            onClose={() => setChargeToDetachCommission(null)}
            onConfirm={handleDetachCommissionConfirm}
            isLoading={detachCommissionMutation.isPending}
            chargeName={chargeToDetachCommission?.name || ''}
            commissionName={chargeToDetachCommission?.commission?.name || ''}
         />
      </div>
   );
}
