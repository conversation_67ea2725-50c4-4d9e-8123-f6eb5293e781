import {
  Injectable,
  Logger,
  OnModuleInit,
  OnModuleDestroy,
} from '@nestjs/common';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { RealtimeGateway } from './realtime.gateway';
import {
  WebSocketDriverOfferEvent,
  WebSocketDriverOfferTimeoutEvent,
} from '@shared/shared/modules/ride-matching/interfaces/ride-events.interface';
import { RideStatusUpdatedDto } from '@shared/shared/common/events/ride-status.update.event';

@Injectable()
export class WebSocketEventConsumerService
  implements OnModuleInit, OnModuleDestroy
{
  private readonly logger = new Logger(WebSocketEventConsumerService.name);
  private isConsuming = false;

  constructor(
    private readonly amqpConnection: AmqpConnection,
    private readonly realtimeGateway: RealtimeGateway,
  ) {
    this.logger.log(
      'WebSocketEventConsumerService initialized - optimized for performance',
    );
  }

  async onModuleInit() {
    await this.startConsuming();
  }

  async onModuleDestroy() {
    await this.stopConsuming();
    this.logger.log('WebSocketEventConsumerService destroyed');
  }

  /**
   * Start consuming WebSocket events from RabbitMQ
   */
  private async startConsuming() {
    if (this.isConsuming) {
      this.logger.warn('Already consuming events');
      return;
    }

    try {
      this.isConsuming = true;

      await this.amqpConnection.createSubscriber(
        async (message) => {
          await this.handleRideStatusUpdate(message as RideStatusUpdatedDto);
        },
        {
          exchange: 'websocket.notifications',
          routingKey: 'websocket.ride.status.update',
          queue: 'websocket.notifications.queue',
          queueOptions: {
            durable: true,
            arguments: {
              'x-message-ttl': 300000,
            },
          },
        },
        'websocket-ride-status-consumer',
      );

      await this.amqpConnection.createSubscriber(
        async (message) => {
          await this.handleDriverOffer(message);
        },
        {
          exchange: 'websocket.notifications',
          routingKey: 'websocket.driver.offer',
          queue: 'websocket.driver.offers.queue',
          queueOptions: {
            durable: true,
            arguments: {
              'x-message-ttl': 300000,
            },
          },
        },
        'websocket-driver-offer-consumer',
      );

      await this.amqpConnection.createSubscriber(
        async (message) => {
          await this.handleDriverOfferTimeout(message);
        },
        {
          exchange: 'websocket.notifications',
          routingKey: 'websocket.driver.offer.timeout',
          queue: 'websocket.driver.timeouts.queue',
          queueOptions: {
            durable: true,
            arguments: {
              'x-message-ttl': 300000,
            },
          },
        },
        'websocket-driver-timeout-consumer',
      );

      await this.amqpConnection.createSubscriber(
        async (message) => {
          await this.handleBatchOffersSent(message);
        },
        {
          exchange: 'websocket.notifications',
          routingKey: 'websocket.batch.offers.sent',
          queue: 'websocket.batch.notifications.queue',
          queueOptions: {
            durable: true,
            arguments: {
              'x-message-ttl': 300000,
            },
          },
        },
        'websocket-batch-offers-consumer',
      );

      await this.amqpConnection.createSubscriber(
        async (message) => {
          await this.handleBatchCompleted(message);
        },
        {
          exchange: 'websocket.notifications',
          routingKey: 'websocket.batch.completed',
          queue: 'websocket.batch.completed.queue',
          queueOptions: {
            durable: true,
            arguments: {
              'x-message-ttl': 300000,
            },
          },
        },
        'websocket-batch-completed-consumer',
      );

      this.logger.log('Started consuming WebSocket events from RabbitMQ');
    } catch (error) {
      this.isConsuming = false;
      this.logger.error('Failed to start consuming WebSocket events:', error);
      throw error;
    }
  }

  /**
   * Stop consuming events
   */
  private async stopConsuming() {
    this.isConsuming = false;
    this.logger.log('Stopped consuming WebSocket events');
  }

  private async handleRideStatusUpdate(message: RideStatusUpdatedDto) {
    const startTime = Date.now();
    try {
      const eventData = message;

      await this.realtimeGateway.broadcastRideStatusUpdate(eventData);

      this.logger.debug(
        `Processed ride status update for ride ${eventData.rideId} (correlationId: ${eventData?.metadata?.correlationId})`,
      );
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(
        `Failed to handle ride status update after ${processingTime}ms:`,
        error,
      );
    }
  }

  /**
   * Handle driver offer events
   */
  private async handleDriverOffer(message: any) {
    try {
      const eventData = message as WebSocketDriverOfferEvent;

      // Validate required fields
      if (!eventData?.driverId || !eventData?.offerId || !eventData?.rideId) {
        this.logger.error(
          'Invalid driver offer event: missing required fields',
          eventData,
        );
        return;
      }
      this.realtimeGateway.notifyDriverOfRideOffer(eventData.driverId, {
        offerId: eventData.offerId,
        rideId: eventData.rideId,
        riderId: eventData.riderId,
        pickupLocation: eventData.pickupLocation,
        destinationLocation: eventData.destinationLocation,
        fareEstimate: eventData.fareEstimate,
        estimatedDuration: eventData.estimatedDuration,
        etaToPickup: eventData.etaToPickup,
        expiresAt: eventData.expiresAt,
        batchNumber: eventData.batchNumber,
        totalBatches: eventData.totalBatches,
        stops: eventData.stops ?? [],
      });

      console.log({ message: 'handleDriverOffer', eventData });
      this.logger.debug(
        `Processed driver offer for driver ${eventData.driverId}`,
      );
    } catch (error) {
      this.logger.error('Failed to handle driver offer event:', error);
      this.logger.error('Message structure:', JSON.stringify(message, null, 2));
    }
  }

  /**
   * Handle driver offer timeout events
   */
  private async handleDriverOfferTimeout(message: any) {
    try {
      // The message IS the event data, not nested under 'data'
      const eventData = message as WebSocketDriverOfferTimeoutEvent;

      // Validate required fields
      if (!eventData?.driverId || !eventData?.offerId || !eventData?.rideId) {
        this.logger.error(
          'Invalid driver offer timeout event: missing required fields',
          eventData,
        );
        return;
      }

      // Direct processing - no duplicate checking needed
      await this.realtimeGateway.notifyDriverOfferTimeout(
        eventData.driverId,
        eventData.offerId,
        eventData.rideId,
      );

      this.logger.debug(
        `Processed driver offer timeout for driver ${eventData.driverId}`,
      );
    } catch (error) {
      this.logger.error('Failed to handle driver offer timeout event:', error);
      this.logger.error('Message structure:', JSON.stringify(message, null, 2));
    }
  }

  getStatistics(): {
    isConsuming: boolean;
    memoryUsage: string;
    queueHealth: any;
  } {
    return {
      isConsuming: this.isConsuming,
      memoryUsage: `${(process.memoryUsage().heapUsed / 1024 / 1024).toFixed(2)} MB`,
      queueHealth: {
        connectionStatus: this.amqpConnection ? 'connected' : 'disconnected',
      },
    };
  }

  /**
   * Handle batch offers sent events
   */
  private async handleBatchOffersSent(message: any) {
    try {
      const eventData = message;

      // Validate required fields
      if (!eventData?.rideId || !eventData?.riderId) {
        this.logger.error(
          'Invalid batch offers sent event: missing required fields',
          eventData,
        );
        return;
      }

      // Direct processing - no duplicate checking needed
      this.logger.debug(
        `Processed batch offers sent for ride ${eventData.rideId}, batch ${eventData.batchNumber}`,
      );
    } catch (error) {
      this.logger.error('Failed to handle batch offers sent event:', error);
      this.logger.error('Message structure:', JSON.stringify(message, null, 2));
    }
  }

  /**
   * Handle batch completed events
   */
  private async handleBatchCompleted(message: any) {
    try {
      const eventData = message;

      // Validate required fields
      if (!eventData?.rideId || !eventData?.riderId) {
        this.logger.error(
          'Invalid batch completed event: missing required fields',
          eventData,
        );
        return;
      }

      // Direct processing - no duplicate checking needed
      this.logger.debug(
        `Processed batch completed for ride ${eventData.rideId}, batch ${eventData.batchNumber}`,
      );
    } catch (error) {
      this.logger.error('Failed to handle batch completed event:', error);
      this.logger.error('Message structure:', JSON.stringify(message, null, 2));
    }
  }
}
