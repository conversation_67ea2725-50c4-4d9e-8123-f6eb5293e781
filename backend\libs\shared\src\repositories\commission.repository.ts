import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { BaseRepository } from './base.repository';
import { Commission, CommissionType } from './models/commission.model';

@Injectable()
export class CommissionRepository extends BaseRepository<Commission> {
  protected readonly modelName = 'commission';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new commission record.
   * @param data - Commission data excluding id and timestamps
   */
  async createCommission(
    data: Omit<Commission, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Commission> {
    return this.create(data);
  }

  /**
   * Find all commissions with optional tax group information.
   */
  async findAllCommissions(): Promise<Commission[]> {
    return this.findMany({
      include: {
        taxGroup: {
          where: { deletedAt: null },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Find commission by ID with tax group information.
   * @param id - Commission ID
   */
  async findCommissionById(id: string): Promise<Commission | null> {
    return this.findUnique({
      where: { id },
      include: {
        taxGroup: {
          where: { deletedAt: null },
        },
      },
    });
  }

  /**
   * Find commission by name.
   * @param name - Commission name
   */
  async findCommissionByName(name: string): Promise<Commission | null> {
    return this.findOne({
      where: { name },
    });
  }

  /**
   * Find commissions by type.
   * @param type - Commission type (percentage or flat)
   */
  async findCommissionsByType(type: CommissionType): Promise<Commission[]> {
    return this.findMany({
      where: { type },
      include: {
        taxGroup: {
          where: { deletedAt: null },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Update commission by ID.
   * @param id - Commission ID
   * @param data - Updated commission data
   */
  async updateCommission(
    id: string,
    data: Partial<
      Omit<Commission, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>
    >,
  ): Promise<Commission> {
    return this.update({
      where: { id },
      data,
      include: {
        taxGroup: {
          where: { deletedAt: null },
        },
      },
    });
  }

  /**
   * Delete commission by ID (soft delete).
   * @param id - Commission ID
   */
  async deleteCommission(id: string): Promise<Commission> {
    return this.softDelete({ where: { id } });
  }

  /**
   * Get paginated commissions.
   * @param page - Page number
   * @param limit - Items per page
   * @param options - Additional options for filtering and searching
   */
  async paginateCommissions(
    page: number,
    limit: number,
    options: {
      search?: string;
      type?: CommissionType;
      taxGroupId?: string;
    } = {},
  ) {
    const { search, type, taxGroupId } = options;

    const whereClause: any = {};

    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (type) {
      whereClause.type = type;
    }

    if (taxGroupId) {
      whereClause.taxGroupId = taxGroupId;
    }

    return this.paginate(page, limit, {
      where: whereClause,
      include: {
        taxGroup: {
          where: { deletedAt: null },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Check if commission is in use (for deletion validation).
   * @param id - Commission ID
   */
  async isCommissionInUse(_id: string): Promise<boolean> {
    // This would check if commission is referenced in other tables
    // For now, we'll return false as there are no known references
    // This can be extended when commission is integrated with other modules
    return false;
  }

  /**
   * Find commissions by tax group ID.
   * @param taxGroupId - Tax group ID
   */
  async findCommissionsByTaxGroupId(taxGroupId: string): Promise<Commission[]> {
    return this.findMany({
      where: { taxGroupId },
      include: {
        taxGroup: {
          where: { deletedAt: null },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }
}
