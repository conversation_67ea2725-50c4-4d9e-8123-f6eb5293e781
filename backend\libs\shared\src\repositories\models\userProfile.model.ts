import { BaseEntity } from '../base.repository';
import { DriverKyc } from './driverKyc.model';
import { Role } from './role.model';
import { User } from './user.model';
import { UserMetaData } from './userMetaData.model';

export enum Gender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER',
}

export enum UserProfileStatus {
  ACTIVE = 'active',
  PENDING = 'pending',
  DISABLED = 'disabled',
  INACTIVE = 'inactive',
  INVITED = 'invited',
}

export interface UserProfile extends BaseEntity {
  userId: string;
  roleId: string;
  firstName: string;
  lastName: string;
  cityId?: string;
  languageId?: string;
  referralCode?: string;
  profilePictureUrl?: string;
  gender?: Gender;
  status: UserProfileStatus;
  dob?: Date;
  isOnline?: boolean;
  role?: Role;
  rideOtp?: string;

  driverKycs?: DriverKyc[];
  user?: User;
  metaData?: UserMetaData;

  // Optionally add city and user relations if needed
}
