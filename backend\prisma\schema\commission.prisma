model Commission {
  id              String         @id @default(uuid()) @map("id") @db.Uuid
  name            String         @map("name")
  description     String?        @map("description")
  type            CommissionType @map("type")
  percentageValue Decimal?       @map("percentage_value") @db.Decimal(5, 2)
  flatValue       Decimal?       @map("flat_value") @db.Decimal(10, 2)
  taxGroupId      String?        @map("tax_group_id") @db.Uuid
  createdAt       DateTime       @default(now()) @map("created_at")
  updatedAt       DateTime       @updatedAt @map("updated_at")
  deletedAt       DateTime?      @map("deleted_at") @db.Timestamptz

  // Relations
  taxGroup TaxGroup? @relation(fields: [taxGroupId], references: [id], onDelete: SetNull)
  charges  Charge[]

  @@unique([name], name: "unique_commission_name")
  @@index([name], name: "idx_commission_name")
  @@index([type], name: "idx_commission_type")
  @@index([taxGroupId], name: "idx_commission_tax_group_id")
  @@map("commissions")
}

enum CommissionType {
  percentage @map("percentage")
  flat       @map("flat")

  @@map("commission_type")
}
