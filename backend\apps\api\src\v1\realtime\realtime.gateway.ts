import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { WsJwtAuthGuard, WsTokenManagerService } from '@shared/shared';
import { RealtimeService } from './realtime.service';
import { RealtimeConnectionStateService } from './realtime-connection-state.service';
import { DriverLocationDto, RideSubscriptionDto } from './dto/realtime.dto';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';
import { RideStatusUpdatedDto } from '@shared/shared/common/events/ride-status.update.event';

@WebSocketGateway({
  namespace: '/realtime',
  cors: {
    origin: '*',
    credentials: true,
  },
  transports: ['websocket', 'polling'],
  allowEIO3: true,
})
export class RealtimeGateway
  implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit
{
  @WebSocketServer()
  server!: Server;

  private readonly logger = new Logger(RealtimeGateway.name);

  constructor(
    private readonly realtimeService: RealtimeService,
    private readonly wsTokenManager: WsTokenManagerService,
    private readonly connectionState: RealtimeConnectionStateService,
  ) {}

  // ========================================
  // GATEWAY INITIALIZATION
  // ========================================

  async afterInit(_server: any) {
    try {
      // Redis adapter is now configured at the application level in main.ts
      // using the custom RedisIoAdapter, so we don't need to configure it here
      this.logger.log('Realtime gateway initialized');
      this.logger.log(
        'Redis adapter configured at application level for horizontal scaling',
      );

      // Log server information for debugging
      if (this.server) {
        this.logger.log(
          `Socket.IO server ready with ${this.server.sockets?.sockets?.size || 0} connections`,
        );
      }
    } catch (error) {
      this.logger.error('Failed to initialize realtime gateway:', error);
      throw error;
    }
  }

  // ========================================
  // CONNECTION MANAGEMENT
  // ========================================

  async handleConnection(client: Socket) {
    try {
      this.logger.log(`Client connected: ${client.id}`);

      // Try to authenticate the socket first (WebSocket auth happens after connection)
      const token = this.extractTokenFromClient(client);
      if (token) {
        const isAuthenticated = await this.wsTokenManager.authenticateSocket(
          client,
          token,
        );
        if (!isAuthenticated) {
          this.logger.warn(`Authentication failed for client ${client.id}`);
          client.disconnect(true);
          return;
        }
      }

      // Now extract user details from authenticated socket with database lookup
      const { profileId, userType } =
        await this.realtimeService.extractUserDetailsWithDbLookup(client);

      if (!profileId) {
        this.logger.warn(
          `No user profile found for client ${client.id} - will retry on message`,
        );
        // Don't disconnect immediately - some clients authenticate after connection
        client.emit('auth_required', {
          success: false,
          message: 'Authentication required. Please provide a valid JWT token.',
          timestamp: new Date().toISOString(),
        });
        return;
      }

      // Store profileId in socket data for later lookup
      client.data.profileId = profileId;
      client.data.userType = userType;

      // Handle driver connections using Redis-based state
      if (userType === 'driver') {
        await this.connectionState.addDriverConnection(profileId, client.id);
        await client.join(`driver:${profileId}`);
        this.logger.log(
          `Driver ${profileId} connected with socket ${client.id}`,
        );
      }
      // Handle rider connections using Redis-based state
      else {
        await this.connectionState.addRiderConnection(profileId, client.id);
        await client.join(`rider:${profileId}`);
        this.logger.log(
          `Rider ${profileId} connected with socket ${client.id}`,
        );
      }

      // Send connection confirmation
      client.emit('connected', {
        success: true,
        message: 'Successfully connected to realtime service',
        profileId,
        userType,
        timestamp: new Date().toISOString(),
        capabilities: [],
      });
    } catch (error) {
      this.logger.error(`Connection error for client ${client.id}:`, error);
      client.emit('connection_error', {
        success: false,
        message: 'Connection failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  }

  async handleDisconnect(client: Socket) {
    try {
      this.logger.log(`Client disconnected: ${client.id}`);

      // Handle token manager cleanup
      this.wsTokenManager.handleSocketDisconnect(client);

      // Use Redis-based cleanup for comprehensive disconnect handling
      await this.connectionState.cleanupSocketData(client.id);

      // Handle driver-specific disconnect logic if needed
      const profileId = client.data?.profileId;
      const userType = client.data?.userType;

      if (profileId && userType === 'driver') {
        this.logger.log(`Driver ${profileId} disconnected`);
        await this.handleDriverDisconnect(profileId);
      } else if (profileId && userType === 'rider') {
        this.logger.log(`Rider ${profileId} disconnected`);
      }
    } catch (error) {
      this.logger.error(`Disconnect error for client ${client.id}:`, error);
    }
  }

  // ========================================
  // DRIVER LOCATION FUNCTIONALITY
  // ========================================

  @SubscribeMessage('location_update')
  @UseGuards(WsJwtAuthGuard)
  async handleLocationUpdate(
    @MessageBody() data: DriverLocationDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const user = (client as any).user;
      const driverId = user?.profileId;

      if (!driverId) {
        client.emit('location_error', {
          success: false,
          message: 'Driver ID not found in authentication',
          timestamp: new Date().toISOString(),
        });
        return;
      }

      const locationData = { ...data, driverId };
      this.logger.log(`Received location update from driver ${driverId}`);

      // Process location update through service
      await this.realtimeService.processLocationUpdate(locationData);

      // If driver is in a ride, broadcast location to ride subscribers
      if (locationData.rideId) {
        await this.connectionState.setDriverRideMapping(
          driverId,
          locationData.rideId,
        );
        await this.broadcastDriverLocationToRide(
          locationData.rideId,
          locationData,
        );
      }

      // Acknowledge location update
      client.emit('location_ack', {
        success: true,
        message: 'Location updated successfully',
        driverId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(
        `Failed to process location update from driver ${data.driverId}:`,
        error,
      );

      client.emit('location_error', {
        success: false,
        message: 'Failed to update location',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('ping')
  async handlePing(@ConnectedSocket() client: Socket) {
    client.emit('pong', {
      success: true,
      message: 'Server is alive',
      timestamp: new Date().toISOString(),
    });
  }

  @SubscribeMessage('health_check')
  async handleHealthCheck(@ConnectedSocket() client: Socket) {
    try {
      const stats = await this.connectionState.getStatistics();
      const healthData = {
        ...stats,
        connectedSockets: this.server.sockets.sockets.size,
        timestamp: new Date().toISOString(),
      };

      client.emit('health_status', {
        success: true,
        data: healthData,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Health check failed:', error);
      client.emit('health_status', {
        success: false,
        error: 'Health check failed',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('subscribe_to_ride')
  @UseGuards(WsJwtAuthGuard)
  async handleRideSubscription(
    @MessageBody() data: RideSubscriptionDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const { rideId } = data;
      const user = (client as any).user;

      if (!rideId) {
        client.emit('subscription_error', {
          success: false,
          message: 'Ride ID is required',
          timestamp: new Date().toISOString(),
        });
        return;
      }

      // Add to ride subscription using Redis-based state
      await this.connectionState.addRideSubscription(rideId, client.id);

      // Join ride-specific room
      await client.join(`ride:${rideId}`);

      this.logger.log(
        `Client ${client.id} (user: ${user?.profileId}) subscribed to ride ${rideId}`,
      );

      client.emit('subscription_success', {
        success: true,
        message: `Successfully subscribed to ride ${rideId}`,
        rideId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(
        `Failed to handle ride subscription for client ${client.id}:`,
        error,
      );

      client.emit('subscription_error', {
        success: false,
        message: 'Failed to subscribe to ride updates',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @SubscribeMessage('unsubscribe_from_ride')
  @UseGuards(WsJwtAuthGuard)
  async handleRideUnsubscription(
    @MessageBody() data: RideSubscriptionDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const { rideId } = data;

      // Remove from ride subscription using Redis-based state
      await this.connectionState.removeRideSubscription(rideId, client.id);

      await client.leave(`ride:${rideId}`);

      this.logger.log(`Client ${client.id} unsubscribed from ride ${rideId}`);

      client.emit('unsubscription_success', {
        success: true,
        message: `Successfully unsubscribed from ride ${rideId}`,
        rideId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(
        `Failed to handle ride unsubscription for client ${client.id}:`,
        error,
      );
    }
  }

  @SubscribeMessage('get_ride_status')
  @UseGuards(WsJwtAuthGuard)
  async handleGetRideStatus(
    @MessageBody() data: { rideId: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const { rideId } = data;

      client.emit('ride_status_response', {
        rideId,
        status: RideStatus.REQUESTED,
        message: 'Ride status retrieved successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(
        `Failed to get ride status for client ${client.id}:`,
        error,
      );

      client.emit('ride_status_error', {
        success: false,
        message: 'Failed to retrieve ride status',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  }

  // ========================================
  // TOKEN MANAGEMENT
  // ========================================

  @SubscribeMessage('token_refresh')
  async handleTokenRefresh(
    @MessageBody() data: { token: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const success = await this.wsTokenManager.refreshSocketToken(
        client,
        data.token,
      );

      if (success) {
        client.emit('token_refresh_success', {
          success: true,
          message: 'Token refreshed successfully',
          timestamp: new Date().toISOString(),
        });
        this.logger.log(`Token refreshed for client ${client.id}`);
      } else {
        client.emit('token_refresh_error', {
          success: false,
          message: 'Token refresh failed',
          timestamp: new Date().toISOString(),
        });
        this.logger.warn(`Token refresh failed for client ${client.id}`);
      }
    } catch (error) {
      this.logger.error(`Token refresh error for client ${client.id}:`, error);
      client.emit('token_refresh_error', {
        success: false,
        message: 'Token refresh failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  }

  // ========================================
  // BROADCASTING METHODS
  // ========================================

  /**
   * Broadcast ride status update to all subscribers
   */
  async broadcastRideStatusUpdate(update: RideStatusUpdatedDto): Promise<void> {
    try {
      if (!this.server) {
        this.logger.error(
          'WebSocket server not initialized, cannot broadcast ride status update',
        );
        return;
      }
      const { rideId, status, message } = update;

      this.server.to(`ride:${rideId}`).emit('ride_status_updated', {
        ...update,
        serverInstance: process.env['NODE_ENV'] || 'development',
        timestamp: new Date().toISOString(),
      });

      this.logger.log(
        `Broadcasted ride status update for ride ${rideId}: ${status} - ${message}`,
      );
    } catch (error) {
      this.logger.error(`Failed to broadcast ride status update:`, error);
    }
  }

  /**
   * Notify driver of new ride offer
   * Works across multiple server instances via Redis adapter
   */
  async notifyDriverOfRideOffer(driverId: string, offer: any): Promise<void> {
    try {
      if (!this.server) {
        this.logger.error('WebSocket server not initialized');
        return;
      }

      // Check if driver is connected before sending notification
      const isConnected =
        await this.connectionState.isDriverConnected(driverId);
      if (!isConnected) {
        this.logger.warn(
          `Driver ${driverId} is not connected, skipping ride offer notification`,
        );
        return;
      }

      // Broadcast to driver room across all server instances
      this.server.to(`driver:${driverId}`).emit('ride_offer_received', {
        ...offer,
        timestamp: new Date().toISOString(),
        serverInstance: process.env['NODE_ENV'] || 'development',
      });

      this.logger.log(
        `Notified driver ${driverId} of ride offer ${offer.offerId || 'no-offer-id'}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to notify driver ${driverId} of ride offer:`,
        error,
      );
    }
  }

  /**
   * Notify driver of offer timeout
   * Works across multiple server instances via Redis adapter
   */
  async notifyDriverOfferTimeout(
    driverId: string,
    offerId: string,
    rideId: string,
  ): Promise<void> {
    try {
      if (!this.server) {
        this.logger.error('WebSocket server not initialized');
        return;
      }

      // Check if driver is connected before sending notification
      const isConnected =
        await this.connectionState.isDriverConnected(driverId);
      if (!isConnected) {
        this.logger.warn(
          `Driver ${driverId} is not connected, skipping timeout notification`,
        );
        return;
      }

      // Broadcast to driver room across all server instances
      this.server.to(`driver:${driverId}`).emit('ride_offer_timeout', {
        offerId,
        rideId,
        message: 'Ride offer has expired',
        timestamp: new Date().toISOString(),
        serverInstance: process.env['NODE_ENV'] || 'development',
      });

      this.logger.log(
        `Notified driver ${driverId} of offer timeout for ride ${rideId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to notify driver ${driverId} of offer timeout:`,
        error,
      );
    }
  }

  /**
   * Broadcast driver location updates to ride subscribers
   * Works across multiple server instances via Redis adapter
   */
  async broadcastDriverLocationToRide(
    rideId: string,
    locationData: DriverLocationDto,
  ): Promise<void> {
    try {
      if (!this.server) {
        this.logger.error('WebSocket server not initialized');
        return;
      }

      // Verify that there are subscribers for this ride
      const subscribers = await this.connectionState.getRideSubscribers(rideId);
      if (subscribers.length === 0) {
        this.logger.debug(
          `No subscribers for ride ${rideId}, skipping location broadcast`,
        );
        return;
      }

      // Broadcast to all subscribers in the ride room across all server instances
      this.server.to(`ride:${rideId}`).emit('driver_location_updated', {
        locationData,
        timestamp: new Date().toISOString(),
        serverInstance: process.env['NODE_ENV'] || 'development',
        subscriberCount: subscribers.length,
      });

      this.logger.log(
        `Broadcasted driver location for ride ${rideId} from driver ${locationData.driverId} to ${subscribers.length} subscribers`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to broadcast driver location for ride ${rideId}:`,
        error,
      );
    }
  }

  /**
   * General purpose location broadcast method (maintains backward compatibility)
   */
  async broadcastDriverLocation(
    rideId: string,
    location: {
      lat: number;
      lng: number;
      heading?: number;
      speed?: number;
      timestamp: string;
    },
  ): Promise<void> {
    try {
      this.server.to(`ride:${rideId}`).emit('driver_location_updated', {
        rideId,
        location,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(
        `Failed to broadcast driver location for ride ${rideId}:`,
        error,
      );
    }
  }

  // ========================================
  // UTILITY & MONITORING METHODS
  // ========================================

  /**
   * Handle driver disconnect logic
   */
  private async handleDriverDisconnect(driverId: string) {
    try {
      // Optional: Update driver status to offline
      // await this.realtimeService.updateDriverStatus(driverId, 'offline');
      this.logger.log(`Processed disconnect for driver ${driverId}`);
    } catch (error) {
      this.logger.error(
        `Failed to handle disconnect for driver ${driverId}:`,
        error,
      );
    }
  }

  /**
   * Get connected clients statistics
   */
  async getStatistics(): Promise<{
    connectedRiders: number;
    connectedDrivers: number;
    totalConnections: number;
    activeRideSubscriptions: number;
    connectedSockets: number;
  }> {
    try {
      const stats = await this.connectionState.getStatistics();
      return {
        ...stats,
        connectedSockets: this.server.sockets.sockets.size,
      };
    } catch (error) {
      this.logger.error('Failed to get statistics:', error);
      return {
        connectedRiders: 0,
        connectedDrivers: 0,
        totalConnections: 0,
        activeRideSubscriptions: 0,
        connectedSockets: this.server.sockets.sockets.size,
      };
    }
  }

  /**
   * Check if a specific driver is connected
   */
  async isDriverConnected(driverId: string): Promise<boolean> {
    try {
      return await this.connectionState.isDriverConnected(driverId);
    } catch (error) {
      this.logger.error(
        `Failed to check driver connection for ${driverId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Check if a specific rider is connected
   */
  async isRiderConnected(riderId: string): Promise<boolean> {
    try {
      return await this.connectionState.isRiderConnected(riderId);
    } catch (error) {
      this.logger.error(
        `Failed to check rider connection for ${riderId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Get all connected drivers
   */
  async getConnectedDrivers(): Promise<string[]> {
    try {
      return await this.connectionState.getConnectedDrivers();
    } catch (error) {
      this.logger.error('Failed to get connected drivers:', error);
      return [];
    }
  }

  /**
   * Get all connected riders
   */
  async getConnectedRiders(): Promise<string[]> {
    try {
      return await this.connectionState.getConnectedRiders();
    } catch (error) {
      this.logger.error('Failed to get connected riders:', error);
      return [];
    }
  }

  /**
   * Health check for WebSocket service
   */
  async isHealthy(): Promise<boolean> {
    try {
      const serverHealthy = this.server && this.server.sockets !== undefined;
      const redisHealthy = await this.connectionState.isHealthy();
      return serverHealthy && redisHealthy;
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return false;
    }
  }

  /**
   * Get driver's current ride if any
   */
  async getDriverCurrentRide(driverId: string): Promise<string | null> {
    try {
      return await this.connectionState.getDriverCurrentRide(driverId);
    } catch (error) {
      this.logger.error(
        `Failed to get driver current ride for ${driverId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Extract JWT token from various Socket.IO client sources
   */
  private extractTokenFromClient(client: Socket): string | null {
    try {
      // Method 1: From handshake auth object (preferred)
      if (client.handshake?.auth['token']) {
        return client.handshake.auth['token'];
      }

      // Method 2: From Authorization header
      const authHeader = client.handshake?.headers?.authorization;
      if (authHeader) {
        // Support "Bearer <token>" format
        const bearerMatch = authHeader.match(/^Bearer\s+(.+)$/i);
        if (bearerMatch) {
          return bearerMatch[1];
        }
        // Support direct token
        return authHeader;
      }

      // Method 3: From query parameters (fallback)
      if (client.handshake?.query?.['token']) {
        return client.handshake.query['token'] as string;
      }

      return null;
    } catch (error) {
      this.logger.error('Error extracting token from client:', error);
      return null;
    }
  }
}
