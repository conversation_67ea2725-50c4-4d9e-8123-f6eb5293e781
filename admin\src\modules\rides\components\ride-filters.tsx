'use client';

import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';

interface RideFiltersProps {
   status: string;
   fromDate: string;
   toDate: string;
   searchQuery: string;
   searchType: string;
   onStatusChange: (value: string) => void;
   onFromDateChange: (value: string) => void;
   onToDateChange: (value: string) => void;
   onSearchQueryChange: (value: string) => void;
   onSearchTypeChange: (value: string) => void;
   onClearFilters: () => void;
}

export function RideFilters({
   status,
   fromDate,
   toDate,
   searchQuery,
   searchType,
   onStatusChange,
   onFromDateChange,
   onToDateChange,
   onSearchQueryChange,
   onSearchTypeChange,
   onClearFilters,
}: RideFiltersProps) {
   const hasActiveFilters = status || fromDate || toDate || searchQuery;

   const dateRange: DateRange | undefined =
      fromDate || toDate
         ? {
              from: fromDate ? new Date(fromDate) : undefined,
              to: toDate ? new Date(toDate) : undefined,
           }
         : undefined;

   const handleDateRangeChange = (range: DateRange | undefined) => {
      onFromDateChange(range?.from ? format(range.from, 'yyyy-MM-dd') : '');
      onToDateChange(range?.to ? format(range.to, 'yyyy-MM-dd') : '');
   };

   const getSearchPlaceholder = () => {
      switch (searchType) {
         case 'riderName':
            return 'Search by rider name...';
         case 'driverName':
            return 'Search by driver name...';
         case 'vehicleNumber':
            return 'Search by vehicle number...';
         default:
            return 'Select search type first...';
      }
   };

   const getSearchLabel = () => {
      if (searchType === 'riderName' || searchType === 'driverName') {
         return 'Search by first name or last name';
      }
      return 'Search';
   };

   return (
      <div className='bg-white rounded-lg border border-gray-200 p-4'>
         <div className='grid grid-cols-1 md:grid-cols-[1fr_0.8fr_1.5fr] gap-4'>
            {/* Date Range Picker */}
            <div className='flex flex-col gap-2'>
               <Label>Date Range</Label>
               <div className='relative'>
                  <Popover>
                     <PopoverTrigger asChild>
                        <Button
                           variant='outline'
                           className={cn(
                              'w-full justify-start text-left font-normal pr-10',
                              !dateRange && 'text-muted-foreground'
                           )}
                        >
                           <CalendarIcon className='mr-2 h-4 w-4' />
                           {dateRange?.from ? (
                              dateRange.to ? (
                                 <>
                                    {format(dateRange.from, 'MMM dd, yyyy')} -{' '}
                                    {format(dateRange.to, 'MMM dd, yyyy')}
                                 </>
                              ) : (
                                 format(dateRange.from, 'MMM dd, yyyy')
                              )
                           ) : (
                              'Pick a date range'
                           )}
                        </Button>
                     </PopoverTrigger>
                     <PopoverContent className='w-auto p-2' align='start'>
                        <Calendar
                           mode='range'
                           selected={dateRange}
                           onSelect={handleDateRangeChange}
                        />
                     </PopoverContent>
                  </Popover>
                  {dateRange && (
                     <button
                        type='button'
                        onClick={() => handleDateRangeChange(undefined)}
                        className='absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 flex items-center justify-center rounded-md hover:bg-gray-100 transition-colors'
                        aria-label='Clear date range'
                     >
                        <svg
                           xmlns='http://www.w3.org/2000/svg'
                           width='14'
                           height='14'
                           viewBox='0 0 24 24'
                           fill='none'
                           stroke='currentColor'
                           strokeWidth='2'
                           strokeLinecap='round'
                           strokeLinejoin='round'
                           className='text-gray-500'
                        >
                           <line x1='18' y1='6' x2='6' y2='18'></line>
                           <line x1='6' y1='6' x2='18' y2='18'></line>
                        </svg>
                     </button>
                  )}
               </div>
            </div>

            {/* Status Filter */}
            <div className='flex flex-col gap-2'>
               <Label>Ride Status</Label>
               <Select value={status} onValueChange={onStatusChange}>
                  <SelectTrigger className='w-full'>
                     <SelectValue placeholder='All Status' />
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Statuses</SelectItem>
                     <SelectItem value='in_progress'>Ongoing</SelectItem>
                     <SelectItem value='trip_completed'>Completed</SelectItem>
                     <SelectItem value='cancelled'>Cancelled</SelectItem>
                     <SelectItem value='scheduled'>Scheduled</SelectItem>
                     <SelectItem value='requested'>Requested</SelectItem>
                     <SelectItem value='accepted'>Accepted</SelectItem>
                  </SelectContent>
               </Select>
            </div>

            {/* Search with Type Selector */}
            <div className='flex flex-col gap-2'>
               <Label>{getSearchLabel()}</Label>
               <div className='flex gap-2'>
                  <Select value={searchType} onValueChange={onSearchTypeChange}>
                     <SelectTrigger className='w-[180px]'>
                        <SelectValue placeholder='Select type' />
                     </SelectTrigger>
                     <SelectContent>
                        <SelectItem value='riderName'>Rider Name</SelectItem>
                        <SelectItem value='driverName'>Driver Name</SelectItem>
                        <SelectItem value='vehicleNumber'>Vehicle Number</SelectItem>
                     </SelectContent>
                  </Select>
                  <Input
                     type='text'
                     value={searchQuery}
                     onChange={e => onSearchQueryChange(e.target.value)}
                     placeholder={getSearchPlaceholder()}
                     className='flex-1'
                  />
               </div>
            </div>
         </div>

         {/* Clear Filters Button */}
         {hasActiveFilters && (
            <div className='mt-4 flex justify-end'>
               <Button onClick={onClearFilters} variant='outline'>
                  Clear Filters
               </Button>
            </div>
         )}
      </div>
   );
}
