import { Test, TestingModule } from '@nestjs/testing';
import { RealtimeConnectionStateService } from './realtime-connection-state.service';
import { RedisService, RedisKeyManagerService } from '@shared/shared';

describe('RealtimeConnectionStateService', () => {
  let service: RealtimeConnectionStateService;
  let redisService: jest.Mocked<RedisService>;
  let keyManager: jest.Mocked<RedisKeyManagerService>;
  let mockRedisClient: any;

  beforeEach(async () => {
    // Mock Redis client
    mockRedisClient = {
      sadd: jest.fn(),
      srem: jest.fn(),
      smembers: jest.fn(),
      scard: jest.fn(),
      sismember: jest.fn(),
      setex: jest.fn(),
      get: jest.fn(),
      del: jest.fn(),
      exists: jest.fn(),
      keys: jest.fn(),
      expire: jest.fn(),
      ping: jest.fn(),
    };

    // Mock RedisService
    const mockRedisService = {
      getClient: jest.fn().mockReturnValue(mockRedisClient),
    };

    // Mock RedisKeyManagerService
    const mockKeyManager = {
      generateKey: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RealtimeConnectionStateService,
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
        {
          provide: RedisKeyManagerService,
          useValue: mockKeyManager,
        },
      ],
    }).compile();

    service = module.get<RealtimeConnectionStateService>(
      RealtimeConnectionStateService,
    );
    redisService = module.get(RedisService);
    keyManager = module.get(RedisKeyManagerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Driver Connection Management', () => {
    it('should add driver connection successfully', async () => {
      const driverId = 'driver-123';
      const socketId = 'socket-456';

      mockRedisClient.sadd.mockResolvedValue(1);
      mockRedisClient.expire.mockResolvedValue(1);
      mockRedisClient.setex.mockResolvedValue('OK');

      await service.addDriverConnection(driverId, socketId);

      expect(mockRedisClient.sadd).toHaveBeenCalledWith(
        `realtime:user_sockets:driver:${driverId}`,
        socketId,
      );
      expect(mockRedisClient.expire).toHaveBeenCalled();
      expect(mockRedisClient.setex).toHaveBeenCalled();
    });

    it('should remove driver connection successfully', async () => {
      const driverId = 'driver-123';
      const socketId = 'socket-456';

      mockRedisClient.srem.mockResolvedValue(1);
      mockRedisClient.scard.mockResolvedValue(0);
      mockRedisClient.del.mockResolvedValue(1);

      await service.removeDriverConnection(driverId, socketId);

      expect(mockRedisClient.srem).toHaveBeenCalledWith(
        `realtime:user_sockets:driver:${driverId}`,
        socketId,
      );
      expect(mockRedisClient.scard).toHaveBeenCalled();
      expect(mockRedisClient.del).toHaveBeenCalledTimes(2); // Both sets should be deleted
    });

    it('should handle multiple connections per driver', async () => {
      const driverId = 'driver-123';
      const socketId1 = 'socket-456';
      const socketId2 = 'socket-789';

      mockRedisClient.scard.mockResolvedValue(1); // Still has connections
      mockRedisClient.smembers.mockResolvedValue([socketId2]);
      mockRedisClient.setex.mockResolvedValue('OK');

      await service.removeDriverConnection(driverId, socketId1);

      expect(mockRedisClient.setex).toHaveBeenCalledWith(
        `realtime:drivers:${driverId}`,
        3600,
        socketId2,
      );
    });

    it('should check if driver is connected', async () => {
      const driverId = 'driver-123';
      mockRedisClient.exists.mockResolvedValue(1);

      const result = await service.isDriverConnected(driverId);

      expect(result).toBe(true);
      expect(mockRedisClient.exists).toHaveBeenCalledWith(
        `realtime:drivers:${driverId}`,
      );
    });

    it('should get all driver sockets', async () => {
      const driverId = 'driver-123';
      const expectedSockets = ['socket-456', 'socket-789'];
      mockRedisClient.smembers.mockResolvedValue(expectedSockets);

      const result = await service.getDriverSockets(driverId);

      expect(result).toEqual(expectedSockets);
      expect(mockRedisClient.smembers).toHaveBeenCalledWith(
        `realtime:user_sockets:driver:${driverId}`,
      );
    });
  });

  describe('Rider Connection Management', () => {
    it('should add rider connection successfully', async () => {
      const riderId = 'rider-123';
      const socketId = 'socket-456';

      mockRedisClient.sadd.mockResolvedValue(1);
      mockRedisClient.expire.mockResolvedValue(1);
      mockRedisClient.setex.mockResolvedValue('OK');

      await service.addRiderConnection(riderId, socketId);

      expect(mockRedisClient.sadd).toHaveBeenCalledWith(
        `realtime:user_sockets:rider:${riderId}`,
        socketId,
      );
      expect(mockRedisClient.expire).toHaveBeenCalled();
      expect(mockRedisClient.setex).toHaveBeenCalled();
    });

    it('should check if rider is connected', async () => {
      const riderId = 'rider-123';
      mockRedisClient.exists.mockResolvedValue(1);

      const result = await service.isRiderConnected(riderId);

      expect(result).toBe(true);
      expect(mockRedisClient.exists).toHaveBeenCalledWith(
        `realtime:riders:${riderId}`,
      );
    });
  });

  describe('Ride Subscription Management', () => {
    it('should add ride subscription successfully', async () => {
      const rideId = 'ride-123';
      const socketId = 'socket-456';

      mockRedisClient.sadd.mockResolvedValue(1);
      mockRedisClient.expire.mockResolvedValue(1);

      await service.addRideSubscription(rideId, socketId);

      expect(mockRedisClient.sadd).toHaveBeenCalledWith(
        `realtime:ride_subscriptions:${rideId}`,
        socketId,
      );
      expect(mockRedisClient.expire).toHaveBeenCalled();
    });

    it('should remove ride subscription successfully', async () => {
      const rideId = 'ride-123';
      const socketId = 'socket-456';

      mockRedisClient.srem.mockResolvedValue(1);
      mockRedisClient.scard.mockResolvedValue(0);
      mockRedisClient.del.mockResolvedValue(1);

      await service.removeRideSubscription(rideId, socketId);

      expect(mockRedisClient.srem).toHaveBeenCalledWith(
        `realtime:ride_subscriptions:${rideId}`,
        socketId,
      );
      expect(mockRedisClient.del).toHaveBeenCalled();
    });

    it('should get ride subscribers', async () => {
      const rideId = 'ride-123';
      const expectedSubscribers = ['socket-456', 'socket-789'];
      mockRedisClient.smembers.mockResolvedValue(expectedSubscribers);

      const result = await service.getRideSubscribers(rideId);

      expect(result).toEqual(expectedSubscribers);
      expect(mockRedisClient.smembers).toHaveBeenCalledWith(
        `realtime:ride_subscriptions:${rideId}`,
      );
    });
  });

  describe('Driver-Ride Mapping', () => {
    it('should set driver ride mapping', async () => {
      const driverId = 'driver-123';
      const rideId = 'ride-456';

      mockRedisClient.setex.mockResolvedValue('OK');

      await service.setDriverRideMapping(driverId, rideId);

      expect(mockRedisClient.setex).toHaveBeenCalledWith(
        `realtime:driver_rides:${driverId}`,
        3600,
        rideId,
      );
    });

    it('should get driver current ride', async () => {
      const driverId = 'driver-123';
      const expectedRideId = 'ride-456';
      mockRedisClient.get.mockResolvedValue(expectedRideId);

      const result = await service.getDriverCurrentRide(driverId);

      expect(result).toBe(expectedRideId);
      expect(mockRedisClient.get).toHaveBeenCalledWith(
        `realtime:driver_rides:${driverId}`,
      );
    });
  });

  describe('Statistics and Utilities', () => {
    it('should get statistics successfully', async () => {
      mockRedisClient.keys
        .mockResolvedValueOnce([
          'realtime:drivers:driver1',
          'realtime:drivers:driver2',
        ])
        .mockResolvedValueOnce(['realtime:riders:rider1'])
        .mockResolvedValueOnce(['realtime:ride_subscriptions:ride1']);

      const result = await service.getStatistics();

      expect(result).toEqual({
        connectedDrivers: 2,
        connectedRiders: 1,
        totalConnections: 3,
        activeRideSubscriptions: 1,
      });
    });

    it('should perform health check', async () => {
      mockRedisClient.ping.mockResolvedValue('PONG');

      const result = await service.isHealthy();

      expect(result).toBe(true);
      expect(mockRedisClient.ping).toHaveBeenCalled();
    });

    it('should handle health check failure', async () => {
      mockRedisClient.ping.mockRejectedValue(
        new Error('Redis connection failed'),
      );

      const result = await service.isHealthy();

      expect(result).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle Redis errors gracefully', async () => {
      const driverId = 'driver-123';
      const socketId = 'socket-456';

      mockRedisClient.sadd.mockRejectedValue(new Error('Redis error'));

      await expect(
        service.addDriverConnection(driverId, socketId),
      ).rejects.toThrow('Redis error');
    });

    it('should return empty array on error for getDriverSockets', async () => {
      const driverId = 'driver-123';
      mockRedisClient.smembers.mockRejectedValue(new Error('Redis error'));

      const result = await service.getDriverSockets(driverId);

      expect(result).toEqual([]);
    });

    it('should return false on error for isDriverConnected', async () => {
      const driverId = 'driver-123';
      mockRedisClient.exists.mockRejectedValue(new Error('Redis error'));

      const result = await service.isDriverConnected(driverId);

      expect(result).toBe(false);
    });
  });
});
