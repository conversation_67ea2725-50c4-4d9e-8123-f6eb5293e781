import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { DriverKycRepository } from '../../repositories/driver-kyc.repository';
import {
  DriverKyc,
  KycStatus,
} from '../../repositories/models/driverKyc.model';
import { KycDocumentService } from '../kyc-document/kyc-document.service';
import { UserProfileService } from '../user-profile/user-profile.service';
import { FileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';
import { UserOnboardingService } from '../user-onboarding/user-onboarding.service';
import { OnboardingStep } from '@shared/shared/repositories/models/userOnboard.model';
import { PrismaService } from '../../database/prisma/prisma.service';
import { UserProfileStatus } from '../../repositories/models/userProfile.model';

@Injectable()
export class DriverKycService {
  constructor(
    private readonly driverKycRepository: DriverKycRepository,
    private readonly kycDocumentService: KycDocumentService,
    private readonly userProfileService: UserProfileService,
    private readonly fileUploadService: FileUploadService,
    private readonly userOnboardingService: UserOnboardingService, // Assuming this service is used for onboarding checks
    private readonly prisma: PrismaService,
  ) {}

  // ==================== ADMIN METHODS ====================

  /**
   * Create driver KYC document (Admin)
   * @param data Driver KYC data
   * @returns Created driver KYC document
   */
  async createDriverKycForAdmin(
    data: Omit<DriverKyc, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<DriverKyc | null> {
    const { kycDocumentId, userProfileId } = data;

    // Validate KYC Document and User Profile
    const [kycDocument, userProfile] = await Promise.all([
      this.kycDocumentService.findKycDocumentById(kycDocumentId),
      this.userProfileService.findUserProfileById(userProfileId),
    ]);

    if (!kycDocument) {
      throw new NotFoundException('KYC document not found');
    }

    if (!userProfile) {
      throw new NotFoundException('User profile not found');
    }
    // Check for existing driver KYC
    const existingDriverKyc =
      await this.driverKycRepository.findDriverKycByUserAndDocument(
        userProfileId,
        kycDocumentId,
      );

    if (existingDriverKyc) {
      throw new BadRequestException(
        'Driver KYC document already exists for this user and document',
      );
    }
    console.log(data);
    if (kycDocument.identifier === 'profile_photo') {
      await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
        userProfile.userId,
        userProfile.roleId,
        OnboardingStep.PROFILE_PHOTO_UPLOAD,
      );
    } else if (kycDocument.isMandatory) {
      await this.checkAndUpdateKycDocumentUploadStatus(userProfileId);
    }

    return await this.driverKycRepository.createDriverKyc(data);
  }

  /**
   * Create a new driver KYC document
   */
  async createDriverKyc(
    data: Omit<DriverKyc, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<DriverKyc | null> {
    const { kycDocumentId, userProfileId } = data;

    // Validate KYC Document and User Profile
    const [kycDocument, userProfile] = await Promise.all([
      this.kycDocumentService.findKycDocumentById(kycDocumentId),
      this.userProfileService.findUserProfileById(userProfileId),
    ]);

    if (!kycDocument) {
      throw new NotFoundException('KYC document not found');
    }

    if (!userProfile) {
      throw new NotFoundException('User profile not found');
    }

    // Check for existing driver KYC
    const existingDriverKyc =
      await this.driverKycRepository.findDriverKycByUserAndDocument(
        userProfileId,
        kycDocumentId,
      );

    if (existingDriverKyc) {
      await this.driverKycRepository.updateDriverKyc(
        existingDriverKyc.id,
        data,
      );
    } else {
      await this.driverKycRepository.createDriverKyc(data);
    }
    if (kycDocument.identifier === 'profile_photo') {
      await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
        userProfile.userId,
        userProfile.roleId,
        OnboardingStep.PROFILE_PHOTO_UPLOAD,
      );
    } else if (kycDocument.isMandatory) {
      await this.checkAndUpdateKycDocumentUploadStatus(userProfileId);
    }
    // Return the latest driver KYC (created or updated)
    return await this.driverKycRepository.findDriverKycByUserAndDocument(
      userProfileId,
      kycDocumentId,
    );
  }

  /**
   * Find all driver KYC documents
   */
  async findAllDriverKycs(): Promise<DriverKyc[]> {
    return this.driverKycRepository.findAllDriverKycs();
  }

  /**
   * Find driver KYC document by ID
   */
  async findDriverKycById(id: string): Promise<DriverKyc> {
    const driverKyc = await this.driverKycRepository.findDriverKycById(id);
    if (!driverKyc) {
      throw new NotFoundException(`Driver KYC with ID ${id} not found`);
    }
    return driverKyc;
  }

  /**
   * Find driver KYC documents by user profile ID
   */
  async findDriverKycsByUserProfileId(
    userProfileId: string,
  ): Promise<DriverKyc[]> {
    // Validate that the user profile exists
    await this.userProfileService.findUserProfileById(userProfileId);

    return this.driverKycRepository.findDriverKycsByUserProfileId(
      userProfileId,
    );
  }

  /**
   * Find driver KYC documents by status
   */
  async findDriverKycsByStatus(status: KycStatus): Promise<DriverKyc[]> {
    return this.driverKycRepository.findDriverKycsByStatus(status);
  }

  /**
   * Update driver KYC document
   */
  async updateDriverKyc(
    id: string,
    data: Partial<DriverKyc>,
  ): Promise<DriverKyc> {
    // Check if driver KYC exists
    await this.findDriverKycById(id);

    // If kycDocumentId is being updated, validate the new KYC document exists
    if (data.kycDocumentId) {
      await this.kycDocumentService.findKycDocumentById(data.kycDocumentId);
    }

    // If userProfileId is being updated, validate the new user profile exists
    if (data.userProfileId) {
      await this.userProfileService.findUserProfileById(data.userProfileId);
    }
    data.status = KycStatus.PENDING;
    if (data.userProfileId) {
      await this.userProfileService.updateUserProfileStatus(
        data.userProfileId,
        UserProfileStatus.PENDING,
      );
    }
    return this.driverKycRepository.updateDriverKyc(id, data);
  }

  /**
   * Approve driver KYC document
   */
  async approveDriverKyc(id: string): Promise<DriverKyc> {
    // Check if driver KYC exists
    const driverKyc = await this.findDriverKycById(id);

    if (driverKyc.kycDocument?.identifier === 'profile_photo') {
      await this.userProfileService.updateProfile(driverKyc.userProfileId, {
        profilePictureUrl: driverKyc.documentUrl,
      });
    }

    return this.driverKycRepository.updateDriverKycStatus(
      id,
      KycStatus.APPROVED,
    );
  }

  /**
   * Reject driver KYC document with rejection note
   */
  async rejectDriverKyc(id: string, rejectionNote: string): Promise<DriverKyc> {
    // Check if driver KYC exists and get it with userProfile relation
    const driverKyc = await this.driverKycRepository.findById(id, {
      include: {
        userProfile: true,
      },
    });

    if (!driverKyc) {
      throw new NotFoundException(`Driver KYC with ID ${id} not found`);
    }

    if (!rejectionNote || rejectionNote.trim().length === 0) {
      throw new BadRequestException(
        'Rejection note is required when rejecting KYC document',
      );
    }

    // Update KYC document status to rejected
    const updatedDriverKyc =
      await this.driverKycRepository.updateDriverKycStatus(
        id,
        KycStatus.REJECTED,
        rejectionNote.trim(),
      );

    // Update user profile status to pending
    if (driverKyc.userProfile) {
      await this.userProfileService.updateUserProfileStatus(
        driverKyc.userProfile.id,
        UserProfileStatus.PENDING,
      );
    }

    return updatedDriverKyc;
  }

  /**
   * Soft delete driver KYC document
   */
  async deleteDriverKyc(id: string): Promise<DriverKyc> {
    // Check if driver KYC exists
    await this.findDriverKycById(id);

    return this.driverKycRepository.deletePermanentDriverKyc(id);
  }

  /**
   * Get paginated driver KYC documents
   */
  async paginateDriverKycs(
    page: number,
    limit: number,
    filters?: {
      userProfileId?: string;
      status?: KycStatus;
      kycDocumentId?: string;
    },
  ) {
    return this.driverKycRepository.paginateDriverKycs(page, limit, filters);
  }

  /**
   * Get driver KYC documents with detailed information including user and document details
   */
  async getDriverKycsWithDetails(filters?: {
    userProfileId?: string;
    status?: KycStatus;
    kycDocumentId?: string;
  }): Promise<DriverKyc[]> {
    const where: any = {};

    if (filters?.userProfileId) {
      where.userProfileId = filters.userProfileId;
    }

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.kycDocumentId) {
      where.kycDocumentId = filters.kycDocumentId;
    }

    return this.driverKycRepository.findMany({
      where,
      include: {
        kycDocument: {
          include: {
            country: true,
          },
        },
        userProfile: {
          include: {
            user: true,
          },
        },
        createdByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * Get all KYC documents with driver KYC data for a specific profile
   * Returns formatted data with first driverKyc as separate key and signed URLs
   */
  async getKycDocumentsWithDriverKycByProfile(userProfileId: string) {
    // Validate that the user profile exists
    await this.userProfileService.findUserProfileById(userProfileId);

    // Get all KYC documents with their related driverKycs for this profile
    const kycDocuments =
      await this.kycDocumentService.findKycDocumentsWithDriverKycByProfileId(
        userProfileId,
      );

    const formattedData = await Promise.all(
      kycDocuments.map(async (kycDoc) => {
        // Find driverKycs for this profile and document
        const driverKycs = kycDoc.driverKycs || [];

        // Get the first driverKyc and format it
        let driverKyc: DriverKyc | null = null;
        if (driverKycs.length > 0) {
          const firstDriverKyc = driverKycs[0];

          // Generate signed URL for document if it exists
          let signedDocumentUrl = null;
          if (firstDriverKyc.documentUrl) {
            try {
              signedDocumentUrl = await this.fileUploadService.getSignedUrl(
                firstDriverKyc.documentUrl,
                3600, // 1 hour expiry
              );
            } catch (error) {
              // If signed URL generation fails, keep the original URL
              signedDocumentUrl = firstDriverKyc.documentUrl;
            }
          }

          driverKyc = {
            ...firstDriverKyc,
            documentUrl: signedDocumentUrl,
          };
        }
        delete kycDoc.driverKycs; // Remove driverKycs from the main object
        return {
          ...kycDoc,
          driverKyc,
        };
      }),
    );

    return formattedData;
  }

  /**
   * Check if all mandatory KYC documents are completed and update onboarding status
   * @param userProfileId User profile ID
   */
  private async checkAndUpdateKycDocumentUploadStatus(
    userProfileId: string,
  ): Promise<void> {
    // Get user profile with city relation to get country
    const userProfile =
      await this.userProfileService.findUserProfileById(userProfileId);
    // need to update country in user profile later
    const indiaCountry = await this.prisma.country.findFirst({
      where: { name: 'India' },
    });

    if (!indiaCountry) {
      console.log(
        'India country not found, skipping KYC document upload status check',
      );
      return;
    }

    // Get all KYC documents for India
    const allKycDocuments =
      await this.kycDocumentService.findKycDocumentsByCountryId(
        indiaCountry.id,
      );

    // Filter mandatory documents
    const mandatoryDocuments = allKycDocuments.filter((doc) => doc.isMandatory);

    // Get all driver KYC documents for this user
    const completedMandatoryDocuments =
      await this.driverKycRepository.findMandatoryDriverKycsByUserProfileId(
        userProfileId,
      );

    // If all mandatory documents are completed, update onboarding status
    if (
      completedMandatoryDocuments.length === mandatoryDocuments.length &&
      mandatoryDocuments.length > 0
    ) {
      await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
        userProfile.userId,
        userProfile.roleId,
        OnboardingStep.KYC_DOCUMENT_UPLOAD,
      );
    }
  }
}
