import { BaseEntity } from '../base.repository';
import { UserProfile } from './userProfile.model';
import { Product } from './product.model';
import { RideLifecycle } from './rideLifecycle.model';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';
import { BookFor } from 'apps/api/src/v1/rides/dto/create-ride.dto';

export interface LocationPoint {
  lat: number;
  lng: number;
  address?: string;
}

export interface RiderMeta {
  name?: string;
  phoneNumber?: string;
  email?: string;
}

export interface Ride extends BaseEntity {
  driverId?: string | null;
  riderId: string;
  productId: string;
  driverVehicleId?: string | null;
  status: RideStatus;
  pickupLocation: LocationPoint | null;
  destinationLocation: LocationPoint | null;
  stops?: LocationPoint[] | null;
  verificationCode?: string | null;
  otpVerifiedAt?: Date | null;
  completedAt?: Date | null;
  duration?: number | null;
  distance?: number | null;
  createdBy?: string | null;
  riderMeta?: RiderMeta | null;
  bookFor?: BookFor;

  // Relations
  driver?: UserProfile | null;
  rider?: UserProfile;
  product?: Product;
  driverVehicle?: any; // Optional relation to driver vehicle
  rideLifecycles?: RideLifecycle[];
}
