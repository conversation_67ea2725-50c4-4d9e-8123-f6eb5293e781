import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { ChargeGroup } from './models/chargeGroup.model';

@Injectable()
export class ChargeGroupRepository extends BaseRepository<ChargeGroup> {
  protected readonly modelName = 'chargeGroup';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new charge group record.
   * @param data - Charge group data excluding id and timestamps
   */
  async createChargeGroup(
    data: Omit<ChargeGroup, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<ChargeGroup> {
    return this.create(data);
  }

  /**
   * Find all charge groups.
   */
  async findAllChargeGroups(): Promise<ChargeGroup[]> {
    return this.findMany();
  }

  /**
   * Find charge group by ID.
   * @param id - Charge group ID
   */
  async findChargeGroupById(
    id: string,
    options?: { select?: any; include?: any; includeSoftDeleted?: boolean },
  ): Promise<ChargeGroup | null> {
    return this.findById(id, options);
  }

  /**
   * Find charge group by identifier.
   * @param identifier - Charge group identifier
   */
  async findChargeGroupByIdentifier(
    identifier: string,
  ): Promise<ChargeGroup | null> {
    return this.findOne({
      where: { identifier },
    });
  }

  /**
   * Update charge group by ID.
   * @param id - Charge group ID
   * @param data - Partial charge group data
   */
  async updateChargeGroup(
    id: string,
    data: Partial<ChargeGroup>,
  ): Promise<ChargeGroup> {
    return this.updateById(id, data);
  }

  /**
   * Soft delete charge group by ID.
   * @param id - Charge group ID
   */
  async deleteChargeGroup(id: string): Promise<ChargeGroup> {
    return this.softDeleteById(id);
  }

  /**
   * Get paginated charge groups.
   * @param page - Page number
   * @param limit - Items per page
   * @param options - Additional query options
   */
  async paginateChargeGroups(page = 1, limit = 10, options?: any) {
    return this.paginate(page, limit, options);
  }

  /**
   * Find charge groups by name (case-insensitive search).
   * @param name - Name to search for
   */
  async findChargeGroupsByName(name: string): Promise<ChargeGroup[]> {
    return this.findMany({
      where: {
        name: {
          contains: name,
          mode: 'insensitive',
        },
      },
    });
  }
}
