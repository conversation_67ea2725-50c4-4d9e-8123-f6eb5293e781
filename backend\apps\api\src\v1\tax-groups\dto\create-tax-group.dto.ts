import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  MaxLength,
  IsOptional,
  IsArray,
  ValidateNested,
  ArrayMinSize,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CreateTaxSubcategoryDto } from './create-tax-subcategory.dto';

export class CreateTaxGroupDto {
  @ApiProperty({
    example: 'GST',
    description: 'Name of the tax group',
    maxLength: 100,
  })
  @IsString({ message: 'Tax group name must be a string' })
  @IsNotEmpty({ message: 'Tax group name is required' })
  @MaxLength(100, { message: 'Tax group name cannot exceed 100 characters' })
  name!: string;

  @ApiProperty({
    example: 'Goods and Services Tax',
    description: 'Description of the tax group',
    maxLength: 500,
    required: false,
  })
  @IsString({ message: 'Tax group description must be a string' })
  @IsOptional()
  @MaxLength(500, {
    message: 'Tax group description cannot exceed 500 characters',
  })
  description?: string;

  @ApiProperty({
    example: [
      { name: 'CGST', percentage: 9.0 },
      { name: 'SGST', percentage: 9.0 },
    ],
    description: 'Array of tax subcategories',
    type: [CreateTaxSubcategoryDto],
  })
  @IsArray({ message: 'Subcategories must be an array' })
  @ArrayMinSize(1, { message: 'At least one subcategory is required' })
  @ValidateNested({ each: true })
  @Type(() => CreateTaxSubcategoryDto)
  subcategories!: CreateTaxSubcategoryDto[];
}
