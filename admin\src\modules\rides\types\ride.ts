export interface Location {
   lat: number;
   lng: number;
   address?: string | null;
}

export interface User {
   id: string;
   phoneNumber?: string | null;
   email?: string | null;
}

export interface UserProfile {
   id: string;
   firstName: string;
   lastName: string;
   profilePictureUrl?: string | null;
   averageRating?: number;
   email?: string | null;
   phoneNumber?: string | null;
   user?: User;
}

export interface VehicleType {
   id: string;
   name: string;
}

export interface DriverVehicle {
   id: string;
   vehicleNumber: string;
   vehicleType: VehicleType;
}

export interface Product {
   id: string;
   name: string;
   description?: string | null;
   icon?: string | null;
}

export interface ReviewBy {
   id: string;
   firstName: string;
   lastName: string;
}

export interface Review {
   id: string;
   rating: number;
   review?: string | null;
   createdAt: string;
   reviewBy: ReviewBy;
}

export interface RideLifecycle {
   id: string;
   status: string;
   createdAt: string;
   meta?: any;
}

export interface RideHistoryItem {
   id: string;
   status: string;
   createdAt: string;
   completedAt?: string | null;
   pickupLocation: Location;
   destinationLocation: Location;
   duration?: number | null;
   distance?: number | null;
   rider: {
      id: string;
      firstName: string;
      lastName: string;
   };
   driver?: {
      id: string;
      firstName: string;
      lastName: string;
   } | null;
   product: {
      id: string;
      name: string;
      icon?: string | null;
   };
}

export interface RideDetails {
   id: string;
   status: string;
   pickupLocation: Location;
   destinationLocation: Location;
   stops?: Location[] | null;
   verificationCode?: string | null;
   createdAt: string;
   completedAt?: string | null;
   otpVerifiedAt?: string | null;
   duration?: number | null;
   distance?: number | null;
   rider: UserProfile;
   driver?: UserProfile | null;
   product: Product;
   driverVehicle?: DriverVehicle | null;
   rideLifecycles: RideLifecycle[];
   reviews: Review[];
}

export interface ListRidesParams {
   page?: number;
   limit?: number;
   status?: string;
   riderName?: string;
   driverName?: string;
   riderId?: string;
   driverId?: string;
   productId?: string;
   fromDate?: string;
   toDate?: string;
   vehicleNumber?: string;
}

export interface ListRidesResponse {
   success: boolean;
   message: string;
   data: RideHistoryItem[];
   meta: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
   };
   timestamp: number;
}

export interface RideDetailsResponse {
   success: boolean;
   message: string;
   data: RideDetails;
   timestamp: number;
}
