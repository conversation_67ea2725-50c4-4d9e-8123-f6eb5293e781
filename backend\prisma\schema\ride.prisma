model Ride {
  id                  String    @id @default(uuid()) @map("id") @db.Uuid
  driverId            String?   @map("driver_id") @db.Uuid
  driverVehicleId     String?   @map("driver_vehicle_id") @db.Uuid
  riderId             String    @map("rider_id") @db.Uuid
  productId           String    @map("product_id") @db.Uuid
  status              String    @default("requested") @map("status")
  pickupLocation      Json?     @map("pickup_location") // Using String for GEOGRAPHY - will store as JSON
  destinationLocation Json?     @map("destination_location") // Using String for GEOGRAPHY - will store as JSON
  stops               Json?     @map("stops") @db.JsonB
  verificationCode    String?   @map("verification_code")
  otpVerifiedAt       DateTime? @map("otp_verified_at") @db.Timestamptz
  matchingAlgorithm   String?   @map("matching_algorithm") // Algorithm used for matching
  matchingStartedAt   DateTime? @map("matching_started_at") // When matching process started
  matchingCompletedAt DateTime? @map("matching_completed_at") // When matching process completed
  duration            Float?    @map("duration")
  distance            Float?    @map("distance")
  createdAt           DateTime  @default(now()) @map("created_at")
  completedAt         DateTime? @map("completed_at") @db.Timestamptz
  updatedAt           DateTime  @updatedAt @map("updated_at")
  deletedAt           DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  driver         UserProfile?    @relation("RideDriver", fields: [driverId], references: [id], onDelete: SetNull)
  rider          UserProfile     @relation("RideRider", fields: [riderId], references: [id], onDelete: Cascade)
  product        Product         @relation(fields: [productId], references: [id])
  driverVehicle  DriverVehicle?  @relation(fields: [driverVehicleId], references: [id], onDelete: SetNull)
  rideLifecycles RideLifecycle[]
  rideOffers     RideOffer[] // New relation to ride offers
  reviews        Review[] // Reviews for this ride

  @@index([driverId], name: "idx_ride_driver_id")
  @@index([driverVehicleId], name: "idx_ride_driver_vehicle_id")
  @@index([riderId], name: "idx_ride_rider_id")
  @@index([productId], name: "idx_ride_product_id")
  @@index([status], name: "idx_ride_status")
  @@index([matchingAlgorithm], name: "idx_ride_matching_algorithm")
  @@index([matchingStartedAt], name: "idx_ride_matching_started_at")
  @@index([createdAt], name: "idx_ride_created_at")
  @@map("rides")
}
