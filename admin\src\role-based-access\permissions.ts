export const RBAC_PERMISSIONS = {
   DRIVER: {
      CREATE: 'driver:create',
      EDIT: 'driver:edit',
      LIST: 'driver:list',
      MANAGE: 'driver:manage',
      STATUS_UPDATE: 'driver:status_update',
   },
   DRIVER_DETAILS: {
      /**
       * same as DRIVER LIST, since by current logic if they have access to see drivers,
       * then they may also see its detailed view. Althought it could have seperate permission in future
       */
      LIST: 'driver:list',
   },
   CITY: {
      CREATE: 'city:create',
      EDIT: 'city:edit',
      LIST: 'city:list',
      MANAGE: 'city:manage',
      STATUS_UPDATE: 'city:status_update',
   },
   CITY_DETAILS: {
      /**
       * same as CITY LIST, since by current logic if they have access to see city,
       * then they may also see its detailed view. Althought it could have seperate permission in future
       */
      LIST: 'city:manage',
   },
   LANGUAGE: {
      CREATE: 'language:create',
      DELETE: 'language:delete',
      EDIT: 'language:edit',
      LIST: 'language:list',
   },
   PRODUCT: {
      CREATE: 'product:create',
      EDIT: 'product:edit',
      LIST: 'product:list',
      STATUS_UPDATE: 'product:status_update',
   },
   PRODUCT_SERVICE: {
      EDIT: 'product_service:edit',
      LIST: 'product_service:list',
   },
   VEHICLE_CATEGORY: {
      CREATE: 'vehicle_category:create',
      EDIT: 'vehicle_category:edit',
      LIST: 'vehicle_category:list',
   },
   ZONE_TYPE: {
      CREATE: 'zone_type:create',
      DELETE: 'zone_type:delete',
      EDIT: 'zone_type:edit',
      LIST: 'zone_type:list',
      STATUS_UPDATE: 'zone_type:status_update',
   },
   CHARGE_GROUPS: {
      CREATE: 'charge_group:create',
      LIST: 'charge_group:list',
      EDIT: 'charge_group:edit',
      DELETE: 'charge_group:delete',
      MANAGE: 'charge_group:manage',
   },
   CHARGE: {
      // CREATE: 'charge:create',
      // LIST: 'charge:list',
      // EDIT: 'charge:edit',
      // DELETE: 'charge:delete',
      CREATE: 'charge_group:create',
      LIST: 'charge_group:list',
      EDIT: 'charge_group:edit',
      DELETE: 'charge_group:delete',
   },
   TAX_GROUP: {
      // CREATE: 'tax_group:create',
      // LIST: 'tax_group:list',
      // EDIT: 'tax_group:edit',
      // DELETE: 'tax_group:delete',
      CREATE: 'charge_group:create',
      LIST: 'charge_group:list',
      EDIT: 'charge_group:edit',
      DELETE: 'charge_group:delete',
   },
   ROLES: {
      CREATE: 'roles:create',
      EDIT: 'roles:edit',
      LIST: 'roles:list',
      MANAGE_PERMISSIONS: 'roles:manage_permissions',
   },
   // both of these are permissions for the admin page CITY_ADMIN && SUB_ADMIN
   CITY_ADMIN: {
      CREATE: 'city_admin:create',
      EDIT: 'city_admin:edit',
      LIST: 'city_admin:list',
      STATUS_UPDATE: 'city_admin:status_update',
   },
   SUB_ADMIN: {
      CREATE: 'sub_admin:create',
      LIST: 'sub_admin:list',
      STATUS_UPDATE: 'sub_admin:status_update',
   },
   RIDES: {
      // LIST: 'rides:list',
      LIST: 'sub_admin:list',
   },
   COMMISSION: {
      // CREATE: 'commission:create',
      // EDIT: 'commission:edit',
      // LIST: 'commission:list',
      // DELETE: 'commission:delete',
      CREATE: 'charge_group:create',
      LIST: 'charge_group:list',
      EDIT: 'charge_group:edit',
      DELETE: 'charge_group:delete',
   },
} as const;
