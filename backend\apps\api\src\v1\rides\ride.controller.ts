import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  Req,
  HttpCode,
  HttpStatus,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import {
  RideSearchService,
  SearchRideParams,
  RideBookingType,
} from '@shared/shared/modules/ride/ride-search.service';
import { RideService } from '@shared/shared/modules/ride/ride.service';
import { RideDetailsService } from '@shared/shared/modules/ride/ride-details.service';
import { SearchRideDto } from './dto/search-ride.dto';
import { SearchRideResponseDto } from './dto/search-ride-response.dto';
import { CreateRideDto } from './dto/create-ride.dto';
import { StartRideDto } from './dto/start-ride.dto';
import {
  RideListResponseDto,
  RideDetailsResponseDto,
  AcceptRideResponseDto,
  StartRideResponseDto,
  CreateRideResponseDto,
} from './dto/ride-response.dto';
import {
  RideHistoryQueryDto,
  RideHistoryResponseDto,
} from './dto/ride-history.dto';
import { RideLifecycleResponseDto } from './dto/ride-details.dto';
import {
  AdminRideHistoryQueryDto,
  AdminRideHistoryResponseDto,
} from './dto/admin-ride-history.dto';
import { AdminRideDetailsResponseDto } from './dto/admin-ride-details.dto';
import {
  DestinationReachedDto,
  DestinationReachedResponseDto,
} from './dto/destination-reached.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { ApiErrorResponseDto } from '../../docs/swagger';
import { Request } from 'express';
import { Ride } from '@shared/shared/repositories/models/ride.model';
import { ApiRequest } from '@shared/shared/modules/auth/interfaces';
@ApiTags('Rides')
@Controller('rides')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RideController {
  constructor(
    private readonly rideSearchService: RideSearchService,
    private readonly rideService: RideService,
    private readonly rideDetailsService: RideDetailsService,
  ) {}

  // ==========================================
  // GET APIS - LIST OPERATIONS
  // ==========================================

  @Get('history/rider')
  @ApiOperation({
    summary: 'Get ride history for rider',
    description:
      'Get paginated ride history for the authenticated rider with optional status filtering',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Comma-separated list of ride statuses to filter by',
    example: 'completed,cancelled',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'Start date filter for ride creation (ISO 8601)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'End date filter for ride creation (ISO 8601)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiResponse({
    status: 200,
    description: 'Rider ride history retrieved successfully',
    type: RideHistoryResponseDto,
  })
  async getRiderRideHistory(
    @Req() req: any,
    @Query() query: RideHistoryQueryDto,
  ): Promise<RideHistoryResponseDto> {
    const riderId = req.user?.profileId;
    if (!riderId) {
      throw new BadRequestException('Rider profile not found');
    }
    console.log(riderId, query);
    const result = await this.rideDetailsService.getRiderRideHistory(
      riderId,
      query,
    );

    return {
      success: true,
      message: 'Rider ride history retrieved successfully',
      data: result.rides,
      meta: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
      timestamp: Date.now(),
    };
  }

  @Get('history/driver')
  @ApiOperation({
    summary: 'Get ride history for driver',
    description:
      'Get paginated ride history for the authenticated driver with optional status filtering',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Comma-separated list of ride statuses to filter by',
    example: 'completed,cancelled',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'Start date filter for ride creation (ISO 8601)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    type: 'string',
    format: 'date-time',
    description: 'End date filter for ride creation (ISO 8601)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver ride history retrieved successfully',
    type: RideHistoryResponseDto,
  })
  async getDriverRideHistory(
    @Req() req: any,
    @Query() query: RideHistoryQueryDto,
  ): Promise<RideHistoryResponseDto> {
    const driverId = req.user?.profileId;
    if (!driverId) {
      throw new BadRequestException('Driver profile not found');
    }

    const result = await this.rideDetailsService.getDriverRideHistory(
      driverId,
      query,
    );

    return {
      success: true,
      message: 'Driver ride history retrieved successfully',
      data: result.rides,
      meta: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
      timestamp: Date.now(),
    };
  }

  @Get('rider/my-rides')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get rides for the current rider',
    description:
      'Fetch all rides for the authenticated rider. Excludes completed and cancelled rides by default.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rider rides retrieved successfully',
    type: RideListResponseDto,
  })
  async getRiderRides(@Req() req: any): Promise<RideListResponseDto> {
    const riderId = req.user.profileId;

    const rides = await this.rideService.getRiderRides(riderId);

    return {
      success: true,
      message: 'Rider rides retrieved successfully',
      data: rides as any, // Service returns Ride[] entities, matches RideResponseDto[] structure
      timestamp: Date.now(),
    };
  }

  @Get('driver/my-rides')
  @ApiOperation({
    summary: 'Get rides for authenticated driver',
    description:
      'Get paginated list of rides for the authenticated driver with optional status filtering',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Comma-separated list of ride statuses to filter by',
    example: 'accepted,in_progress',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Driver rides retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Driver rides retrieved successfully',
        },
        data: {
          type: 'object',
          properties: {
            rides: {
              type: 'array',
              items: { $ref: '#/components/schemas/RideResponseDto' },
            },
            total: { type: 'number', example: 25 },
            page: { type: 'number', example: 1 },
            limit: { type: 'number', example: 10 },
          },
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'Unauthorized',
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async getDriverRides(@Req() req: any): Promise<any> {
    const driverId = req.user?.profileId;
    if (!driverId) {
      throw new BadRequestException('Driver profile not found');
    }

    const result = await this.rideService.getRidesForDriver(driverId);

    return {
      success: true,
      message: 'Driver rides retrieved successfully',
      data: result,
      timestamp: Date.now(),
    };
  }

  // ==========================================
  // GET APIS - BY ID OPERATIONS
  // ==========================================

  @Get(':rideId/rider')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get ride details by ID',
    description:
      'Fetch detailed information about a specific ride including driver, rider, product details, and lifecycle history.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Ride details retrieved successfully',
    type: RideDetailsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Ride not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'Ride with ID ride-uuid-123 not found',
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async getRideDetails(
    @Param('rideId') rideId: string,
  ): Promise<RideDetailsResponseDto> {
    const ride = await this.rideService.getRideById(rideId);

    return {
      success: true,
      message: 'Ride details retrieved successfully',
      data: ride as any, // Service returns Ride entity, matches RideResponseDto structure
      timestamp: Date.now(),
    };
  }

  @Get(':rideId/driver')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get ride details by ID for driver view',
    description:
      'Fetch detailed information about a specific ride including rider, product details, and lifecycle history for driver view.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Ride details retrieved successfully',
    type: RideDetailsResponseDto,
  })
  async getRideDetailsForDriverView(
    @Param('rideId') rideId: string,
  ): Promise<RideDetailsResponseDto> {
    const ride = await this.rideService.getRideById(rideId);

    return {
      success: true,
      message: 'Ride details retrieved successfully',
      data: ride as any, // Service returns Ride entity, matches RideResponseDto structure
      timestamp: Date.now(),
    };
  }

  @Get(':rideId/details/rider')
  @ApiOperation({
    summary: 'Get detailed ride information for rider view',
    description:
      'Get comprehensive ride details including driver info, vehicle details, and ratings for rider view',
  })
  @ApiParam({
    name: 'rideId',
    description: 'Unique identifier of the ride',
    example: 'ride-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Detailed ride information retrieved successfully',
    type: RideDetailsResponseDto,
  })
  async getRideDetailsForRider(
    @Param('rideId') rideId: string,
    @Req() req: ApiRequest,
  ): Promise<RideDetailsResponseDto> {
    const result = await this.rideDetailsService.getRideDetailsForRider(
      rideId,
      req.user.profileId,
    );

    return {
      success: true,
      message: 'Detailed ride information retrieved successfully',
      data: result,
      timestamp: Date.now(),
    };
  }

  @Get(':rideId/details/driver')
  @ApiOperation({
    summary: 'Get detailed ride information for driver view',
    description:
      'Get comprehensive ride details including rider info, vehicle details, and ratings for driver view',
  })
  @ApiParam({
    name: 'rideId',
    description: 'Unique identifier of the ride',
    example: 'ride-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Detailed ride information retrieved successfully',
    type: RideDetailsResponseDto,
  })
  async getRideDetailsForDriver(
    @Param('rideId') rideId: string,
  ): Promise<RideDetailsResponseDto> {
    const result =
      await this.rideDetailsService.getRideDetailsForDriver(rideId);

    return {
      success: true,
      message: 'Detailed ride information retrieved successfully',
      data: result,
      timestamp: Date.now(),
    };
  }

  @Get(':rideId/ride-life-cycle')
  @ApiOperation({
    summary: 'Get ride lifecycle history',
    description:
      'Get complete lifecycle history of a ride ordered by creation date ascending',
  })
  @ApiParam({
    name: 'rideId',
    description: 'Unique identifier of the ride',
    example: 'ride-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Ride lifecycle history retrieved successfully',
    type: RideLifecycleResponseDto,
  })
  async getRideLifecycle(
    @Param('rideId') rideId: string,
  ): Promise<RideLifecycleResponseDto> {
    const result = await this.rideDetailsService.getRideLifecycle(rideId);

    return {
      success: true,
      message: 'Ride lifecycle history retrieved successfully',
      data: result,
      timestamp: Date.now(),
    };
  }

  // ==========================================
  // POST APIS - ACTIONS AND CREATION
  // ==========================================

  @Post('search')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Search for available rides',
    description:
      'Search for available ride options based on pickup and destination locations. Determines service type (city, rental, intercity) based on zone analysis.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Ride search completed successfully',
    type: SearchRideResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid search parameters',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'Pickup time is required when type is later',
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Authentication required',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Unauthorized' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async searchRides(
    @Body() searchDto: SearchRideDto,
  ): Promise<SearchRideResponseDto> {
    // Prepare search parameters
    const searchParams: SearchRideParams = {
      pickup: {
        lat: searchDto.pickup.lat,
        lng: searchDto.pickup.lng,
      },
      destination: {
        lat: searchDto.destination.lat,
        lng: searchDto.destination.lng,
      },
      type: searchDto.type
        ? (searchDto.type as unknown as RideBookingType)
        : RideBookingType.NOW,
      pickupTime: searchDto.pickupTime,
    };

    // Execute search logic
    const rideOptions = await this.rideSearchService.searchRides(searchParams);

    return {
      success: true,
      message: 'Ride search completed successfully',
      data: rideOptions as any, // Service returns RideSearchResult[] which matches RideSearchResultDto[]
      timestamp: Date.now(),
    };
  }

  @Post('request')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new ride',
    description:
      'Create a new ride request with pickup, destination, and optional stops. The ride will be created with status "requested" and a lifecycle entry will be added.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Ride created successfully',
    type: CreateRideResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid ride data',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: {
          type: 'string',
          example: 'Product not found',
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async requestRide(
    @Body() createRideDto: CreateRideDto,
    @Req() req: Request,
    // ): Promise<CreateRideResponseDto> {
  ): Promise<any> {
    // Get rider profile ID from JWT token

    //TODO: ACCESS VIA AN INTERFACE REF: JWT PAYLOAD
    const riderId = (req.user as any)?.profileId;

    const ride = await this.rideService.requestRide({
      riderId,
      productId: createRideDto.productId,
      pickup: createRideDto.pickup,
      destination: createRideDto.destination,
      stops: createRideDto.stops || undefined,
      ...(createRideDto.riderMeta && { riderMeta: createRideDto.riderMeta }),
      ...(createRideDto.bookFor && { bookFor: createRideDto.bookFor }),
    });

    return {
      success: true,
      message: 'Ride requested successfully',
      data: ride as Ride,
      timestamp: Date.now(),
    };
  }

  // ==========================================
  // RIDE LIFECYCLE MANAGEMENT
  // ==========================================

  @Post(':rideId/accept')
  @ApiOperation({
    summary: 'Accept a ride',
    description:
      'Driver accepts a ride request. Updates ride status to accepted and assigns driver.',
  })
  @ApiParam({
    name: 'rideId',
    description: 'ID of the ride to accept',
    example: 'ride-uuid-123',
  })
  @ApiResponse({
    status: 200,
    type: AcceptRideResponseDto,
    description: 'Ride accepted successfully',
  })
  @ApiResponse({
    status: 400,
    type: ApiErrorResponseDto,
    description: 'Bad request - ride already accepted or invalid status',
  })
  @ApiResponse({
    status: 404,
    type: ApiErrorResponseDto,
    description: 'Ride or driver not found',
  })
  async acceptRide(
    @Param('rideId') rideId: string,
    @Req() req: any,
    @Body() body: any,
  ): Promise<AcceptRideResponseDto> {
    const driverId = req.user.profileId;

    if (!driverId) {
      throw new BadRequestException('Driver profile not found');
    }

    const ride = await this.rideService.acceptRide(
      rideId,
      driverId,
      body?.driverVehicleId,
    );

    // Remove verification code from response
    const { verificationCode, ...rideWithoutCode } = ride;

    return {
      success: true,
      message: 'Ride accepted successfully',
      data: rideWithoutCode as any,
      timestamp: Date.now(),
    };
  }

  @Post(':rideId/end-ride')
  @ApiOperation({
    summary: 'End a ride by driver',
    description:
      'Driver ends a ride. Optionally update destination; previous destination is added to stops. Status set to completed and lifecycle entry created.',
  })
  @ApiParam({
    name: 'rideId',
    description: 'ID of the ride to end',
    example: 'ride-uuid-123',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        newDestination: {
          type: 'object',
          required: ['lat', 'lng'],
          properties: {
            lat: { type: 'number', example: 12.9716 },
            lng: { type: 'number', example: 77.5946 },
            address: { type: 'string', example: 'New address', nullable: true },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    type: RideDetailsResponseDto,
    description: 'Ride ended successfully',
  })
  @UseGuards(JwtAuthGuard)
  async endRide(
    @Param('rideId') rideId: string,
    @Req() req: any,
    @Body() body: any,
  ): Promise<RideDetailsResponseDto> {
    const driverId = req.user.profileId;
    if (!driverId) throw new BadRequestException('Driver profile not found');

    const newDestination = body?.newDestination;
    const ride = await this.rideService.endRide(
      rideId,
      driverId,
      newDestination,
    );

    return {
      success: true,
      message: 'Ride ended successfully',
      data: ride as any,
      timestamp: Date.now(),
    };
  }

  @Post(':rideId/start-ride')
  @ApiOperation({
    summary: 'Start a ride',
    description:
      'Driver starts a ride by verifying the OTP provided by the rider. Updates ride status to in_progress and records OTP verification timestamp.',
  })
  @ApiParam({
    name: 'rideId',
    description: 'ID of the ride to start',
    example: 'ride-uuid-123',
  })
  @ApiResponse({
    status: 200,
    type: StartRideResponseDto,
    description: 'Ride started successfully',
  })
  @ApiResponse({
    status: 400,
    type: ApiErrorResponseDto,
    description: 'Bad request - Invalid OTP or ride not assigned to driver',
  })
  @ApiResponse({
    status: 404,
    type: ApiErrorResponseDto,
    description: 'Ride not found',
  })
  async startRide(
    @Param('rideId') rideId: string,
    @Body() startRideDto: StartRideDto,
    @Req() req: any,
  ): Promise<StartRideResponseDto> {
    // Get driver profile ID from JWT token
    const driverId = req?.user?.profileId;
    if (!driverId) {
      throw new BadRequestException('Driver profile not found');
    }

    const ride = await this.rideService.startRide(
      rideId,
      driverId,
      startRideDto.otp,
    );

    return {
      success: true,
      message: 'Ride started successfully',
      data: ride as any, // Service returns Ride entity, matches RideResponseDto structure
      timestamp: Date.now(),
    };
  }

  // ==========================================
  // RIDE CANCELLATION OPERATIONS
  // ==========================================

  @Post(':rideId/cancel/rider')
  @ApiOperation({
    summary: 'Cancel ride by rider',
    description:
      'Rider cancels their ride. Status changes to cancelled and lifecycle entry is added.',
  })
  @ApiParam({
    name: 'rideId',
    description: 'ID of the ride to cancel',
    example: 'ride-uuid-123',
  })
  @ApiResponse({
    status: 200,
    type: RideDetailsResponseDto,
    description: 'Ride cancelled successfully by rider',
  })
  @ApiResponse({
    status: 400,
    type: ApiErrorResponseDto,
    description: 'Bad request - cannot cancel ride or not authorized',
  })
  @ApiResponse({
    status: 404,
    type: ApiErrorResponseDto,
    description: 'Ride not found',
  })
  async cancelRideByRider(
    @Param('rideId') rideId: string,
    @Req() req: any,
    @Body() body?: { reason?: string },
  ): Promise<RideDetailsResponseDto> {
    const riderId = req.user.profileId;

    if (!riderId) {
      throw new BadRequestException('Rider profile not found');
    }

    const ride = await this.rideService.cancelRideByRider(
      rideId,
      riderId,
      body?.reason,
    );

    return {
      success: true,
      message: 'Ride cancelled successfully by rider',
      data: ride as any,
      timestamp: Date.now(),
    };
  }

  @Post(':rideId/cancel/driver')
  @ApiOperation({
    summary: 'Cancel ride by driver',
    description:
      'Driver cancels assigned ride. Status changes to processing for reassignment and lifecycle entry is added.',
  })
  @ApiParam({
    name: 'rideId',
    description: 'ID of the ride to cancel',
    example: 'ride-uuid-123',
  })
  @ApiResponse({
    status: 200,
    type: RideDetailsResponseDto,
    description: 'Ride cancelled successfully by driver',
  })
  @ApiResponse({
    status: 400,
    type: ApiErrorResponseDto,
    description: 'Bad request - cannot cancel ride or not authorized',
  })
  @ApiResponse({
    status: 404,
    type: ApiErrorResponseDto,
    description: 'Ride not found',
  })
  async cancelRideByDriver(
    @Param('rideId') rideId: string,
    @Req() req: any,
    @Body() body?: { reason?: string },
  ): Promise<RideDetailsResponseDto> {
    const driverId = req.user.profileId;

    if (!driverId) {
      throw new BadRequestException('Driver profile not found');
    }

    const ride = await this.rideService.cancelRideByDriver(
      rideId,
      driverId,
      body?.reason,
    );

    return {
      success: true,
      message:
        'Ride cancelled successfully by driver, status changed to processing for reassignment',
      data: ride as any,
      timestamp: Date.now(),
    };
  }

  // ==================== ADMIN APIs ====================

  @Get('history/admin')
  @ApiOperation({
    summary: 'Get paginated ride history for admin',
    description:
      'Retrieve paginated list of rides with comprehensive filtering options for admin users. Includes rider names, driver names, product details, and various filters.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by ride status',
    example: 'completed',
  })
  @ApiQuery({
    name: 'riderName',
    required: false,
    type: String,
    description: 'Search by rider name',
    example: 'John',
  })
  @ApiQuery({
    name: 'driverName',
    required: false,
    type: String,
    description: 'Search by driver name',
    example: 'Jane',
  })
  @ApiQuery({
    name: 'riderId',
    required: false,
    type: String,
    description: 'Filter by rider ID',
    example: 'rider-uuid-123',
  })
  @ApiQuery({
    name: 'driverId',
    required: false,
    type: String,
    description: 'Filter by driver ID',
    example: 'driver-uuid-123',
  })
  @ApiQuery({
    name: 'productId',
    required: false,
    type: String,
    description: 'Filter by product ID',
    example: 'product-uuid-123',
  })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    type: String,
    description: 'Start date for filtering (ISO 8601)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    type: String,
    description: 'End date for filtering (ISO 8601)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiResponse({
    status: 200,
    description: 'Ride history retrieved successfully',
    type: AdminRideHistoryResponseDto,
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, type: ApiErrorResponseDto })
  async getAdminRideHistory(
    @Query() query: AdminRideHistoryQueryDto,
  ): Promise<AdminRideHistoryResponseDto> {
    const result = await this.rideDetailsService.getAdminRideHistory(query);

    return {
      success: true,
      message: 'Rides retrieved successfully',
      data: result.rides as any,
      meta: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
        hasNextPage: result.page < result.totalPages,
        hasPreviousPage: result.page > 1,
      },
      timestamp: Date.now(),
    };
  }

  @Get(':rideId/details/admin')
  @ApiOperation({
    summary: 'Get detailed ride information for admin',
    description:
      'Retrieve comprehensive ride details including rider and driver profiles, ratings, reviews, ride lifecycles, product information, and vehicle details.',
  })
  @ApiParam({
    name: 'rideId',
    description: 'Ride ID',
    example: 'ride-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Ride details retrieved successfully',
    type: AdminRideDetailsResponseDto,
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, type: ApiErrorResponseDto })
  async getAdminRideDetails(
    @Param('rideId') rideId: string,
  ): Promise<AdminRideDetailsResponseDto> {
    const rideDetails =
      await this.rideDetailsService.getAdminRideDetails(rideId);

    return {
      success: true,
      message: 'Ride details retrieved successfully',
      data: rideDetails,
      timestamp: Date.now(),
    };
  }

  @Post(':rideId/destination-reached')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Mark destination as reached',
    description:
      'Driver marks that they have reached a destination stop. The API validates the location against ride stops using H3 geospatial indexing at resolution 9, creates a ride lifecycle entry, publishes RabbitMQ events, and sends notifications to the rider.',
  })
  @ApiParam({
    name: 'rideId',
    description: 'Ride ID',
    example: 'ride-uuid-123',
  })
  @ApiBody({
    type: DestinationReachedDto,
    description: 'Current location coordinates',
  })
  @ApiResponse({
    status: 200,
    description: 'Destination reached successfully',
    type: DestinationReachedResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - location does not match any stop or invalid ride status',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, type: ApiErrorResponseDto })
  async markDestinationReached(
    @Param('rideId') rideId: string,
    @Body() body: DestinationReachedDto,
    @Req() request: ApiRequest,
  ): Promise<DestinationReachedResponseDto> {
    const driverId = request.user.profileId;

    if (!driverId) {
      throw new BadRequestException('Driver profile ID not found in token');
    }

    const ride = await this.rideService.markDestinationReached(
      rideId,
      driverId,
      body.location,
    );

    return {
      success: true,
      message: 'Destination reached successfully',
      data: ride,
      timestamp: Date.now(),
    };
  }
}
