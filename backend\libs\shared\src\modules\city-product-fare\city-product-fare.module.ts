import { Module } from '@nestjs/common';
import { CityProductFareService } from './city-product-fare.service';
import { CityProductFareRepository } from '../../repositories/city-product-fare.repository';
import { CityProductRepository } from '../../repositories/city-product.repository';
import { ChargeGroupRepository } from '../../repositories/charge-group.repository';
import { ZoneRepository } from '../../repositories/zone.repository';
import { ZoneTypeRepository } from '../../repositories/zone-type.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';

@Module({
  providers: [
    CityProductFareService,
    CityProductFareRepository,
    CityProductRepository,
    ChargeGroupRepository,
    ZoneRepository,
    ZoneTypeRepository,
    PrismaService,
    H3UtilityService,
  ],
  exports: [CityProductFareService, CityProductFareRepository],
})
export class CityProductFareModule {}
