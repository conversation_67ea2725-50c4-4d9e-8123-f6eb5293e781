import { Module } from '@nestjs/common';
import {
  UserRepository,
  AuthCredentialRepository,
  RefreshTokenRepository,
  RoleRepository,
  PermissionRepository,
  ProductRepository,
  KycDocumentRepository,
  DriverKycRepository,
  RideRepository,
  RideLifecycleRepository,
  CellDriverRepository,
  DriverCellMappingRepository,
  DriverMetadataRepository,
  ReviewRepository,
  UserMetaDataRepository,
  ChargeGroupRepository,
  TaxGroupRepository,
  TaxSubcategoryRepository,
  CommissionRepository,
} from './index';
import { RedisModule } from '../database/redis/redis.module';
import { PrismaModule } from '../database/prisma/prisma.module';
import { RideMatchCacheRepository } from './ride-match-cache.repository';
import { RedisKeyManagerService } from '../database/redis/redis-key-manager.service';
import { RideOfferRepository } from './ride-offer.repository';
import { UserProfileRepository } from './user-profile.repository';
import { ChargeRepository } from './charge.repository';

@Module({
  imports: [RedisModule, PrismaModule],
  providers: [
    UserRepository,
    AuthCredentialRepository,
    RefreshTokenRepository,
    RoleRepository,
    PermissionRepository,
    ProductRepository,
    KycDocumentRepository,
    DriverKycRepository,
    RideRepository,
    RideLifecycleRepository,
    CellDriverRepository,
    DriverCellMappingRepository,
    DriverMetadataRepository,
    RideMatchCacheRepository,
    RedisKeyManagerService,
    RideOfferRepository,
    UserProfileRepository,
    RideRepository,
    ReviewRepository,
    UserMetaDataRepository,
    ChargeGroupRepository,
    ChargeRepository,
    TaxGroupRepository,
    TaxSubcategoryRepository,
    CommissionRepository,
  ],
  exports: [
    UserRepository,
    AuthCredentialRepository,
    RefreshTokenRepository,
    RoleRepository,
    PermissionRepository,
    ProductRepository,
    KycDocumentRepository,
    DriverKycRepository,
    RideRepository,
    RideLifecycleRepository,
    CellDriverRepository,
    DriverCellMappingRepository,
    DriverMetadataRepository,
    RideMatchCacheRepository,
    RedisKeyManagerService,
    RideOfferRepository,
    UserProfileRepository,
    RideRepository,
    ReviewRepository,
    UserMetaDataRepository,
    ChargeGroupRepository,
    ChargeRepository,
    TaxGroupRepository,
    TaxSubcategoryRepository,
    CommissionRepository,
  ],
})
export class RepositoryModule {}
