"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
   SidebarGroup,
   SidebarMenu,
   SidebarMenuButton,
   SidebarMenuItem,
   SidebarMenuSub,
   SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import Link from 'next/link';

export function NavMain({
   items,
}: {
   items: {
      title: string;
      url: string;
      icon?: LucideIcon;
      isActive?: boolean;
      items?: {
         title: string;
         url: string;
      }[];
   }[];
}) {
   return (
      <SidebarGroup className='py-2 group-data-[collapsible=icon]:px-1'>
         <SidebarMenu className='space-y-1'>
            {items.map(item => {
               // If item has sub-items, render as collapsible
               if (item.items && item.items.length > 0) {
                  return (
                     <Collapsible
                        key={item.title}
                        asChild
                        defaultOpen={item.isActive}
                        className='group/collapsible'
                     >
                        <SidebarMenuItem>
                           <CollapsibleTrigger asChild>
                              <SidebarMenuButton
                                 tooltip={item.title}
                                 className='h-8 px-3 py-1 text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md font-medium cursor-pointer'
                              >
                                 {item.icon && <item.icon className='w-4 h-4' />}
                                 <span className='text-sm group-data-[collapsible=icon]:hidden'>
                                    {item.title}
                                 </span>
                                 <ChevronRight className='ml-auto w-3 h-3 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 group-data-[collapsible=icon]:hidden' />
                              </SidebarMenuButton>
                           </CollapsibleTrigger>
                           <CollapsibleContent>
                              <SidebarMenuSub>
                                 {item.items.map((subItem: any) => (
                                    <SidebarMenuSubItem key={subItem.title}>
                                       <Link
                                          href={subItem.url}
                                          className={`flex h-7 min-w-0 items-center gap-2.5 overflow-hidden rounded-md px-2 text-sm outline-hidden transition-colors ${
                                             subItem.isActive
                                                ? 'bg-blue-50 text-blue-700 font-medium'
                                                : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                                          }`}
                                       >
                                          {subItem.icon && (
                                             <subItem.icon
                                                className={`w-4 h-4 flex-shrink-0 ${
                                                   subItem.isActive
                                                      ? '!text-blue-700'
                                                      : '!text-gray-400'
                                                }`}
                                             />
                                          )}
                                          <span className='truncate'>{subItem.title}</span>
                                       </Link>
                                    </SidebarMenuSubItem>
                                 ))}
                              </SidebarMenuSub>
                           </CollapsibleContent>
                        </SidebarMenuItem>
                     </Collapsible>
                  );
               }

               // If no sub-items, render as direct link
               return (
                  <SidebarMenuItem key={item.title}>
                     <SidebarMenuButton
                        asChild
                        tooltip={item.title}
                        isActive={item.isActive}
                        className={`h-8 px-3 py-1 rounded-md font-medium transition-colors cursor-pointer group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:mx-auto group-data-[collapsible=icon]:w-8 ${
                           item.isActive
                              ? 'bg-blue-50 text-blue-700 border border-blue-200'
                              : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                        }`}
                     >
                        <Link
                           href={item.url}
                           className='flex items-center gap-2 w-full group-data-[collapsible=icon]:justify-center'
                        >
                           {item.icon && <item.icon className='w-4 h-4' />}
                           <span className='text-sm group-data-[collapsible=icon]:hidden'>
                              {item.title}
                           </span>
                        </Link>
                     </SidebarMenuButton>
                  </SidebarMenuItem>
               );
            })}
         </SidebarMenu>
      </SidebarGroup>
   );
}
