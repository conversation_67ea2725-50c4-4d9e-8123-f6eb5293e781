import { Module } from '@nestjs/common';
import { RideSearchService } from './ride-search.service';
import { RideService } from './ride.service';
import { RideDetailsService } from './ride-details.service';
import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';
import { H3IndexToZoneRepository } from '@shared/shared/repositories/h3IndexToZone.repository';
import { LocationIngestorService } from '../location/location-ingestor.service';
import { ProductModule } from '../product/product.module';
import { FileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';
import { CellDriverRepository } from '@shared/shared/repositories/cell-driver.repository';

import { DriverMetadataRepository } from '@shared/shared/repositories/driver-metadata.repository';
import { RedisService } from '@shared/shared/database/redis/redis.service';
import { GoogleRouteMatrixService } from '@shared/shared/common/google/google-route-matrix.service';
import { AppConfigModule } from '@shared/shared/config';
import { RideRepository } from '@shared/shared/repositories/ride.repository';
import { RideLifecycleRepository } from '@shared/shared/repositories/ride-lifecycle.repository';
import { UserProfileRepository } from '@shared/shared/repositories/user-profile.repository';
import { UserRepository } from '@shared/shared/repositories/user.repository';
import { ProductRepository } from '@shared/shared/repositories/product.repository';
import {
  DriverCellMappingRepository,
  ReviewRepository,
} from '@shared/shared/repositories';
import { NotificationService } from '@shared/shared/common/notifications/engagespot/engagespot.service';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { GlobalEventEmitterModule } from '@shared/shared/event-emitter/event-emitter.module';
import { DriverVehicleRepository } from '@shared/shared/repositories/driver-vehicle.repository';
import { UserMetaDataModule } from '../user-meta-data/user-meta-data.module';
import { RideOfferRepository } from '@shared/shared/repositories/ride-offer.repository';
import { DriverCityProductRepository } from '@shared/shared/repositories/driver-city-product.repository';

@Module({
  imports: [
    ProductModule,
    AppConfigModule,
    GlobalEventEmitterModule,
    UserMetaDataModule,
  ],
  providers: [
    RideSearchService,
    RideService,
    RideDetailsService,
    H3UtilityService,
    H3IndexToZoneRepository,
    LocationIngestorService,
    FileUploadService,
    CellDriverRepository,
    DriverMetadataRepository,
    RedisService,
    GoogleRouteMatrixService,
    RideRepository,
    RideLifecycleRepository,
    UserProfileRepository,
    UserRepository,
    ProductRepository,
    DriverCellMappingRepository,
    NotificationService,
    PrismaService,
    DriverVehicleRepository,
    RideOfferRepository,
    ReviewRepository,
    DriverCityProductRepository,
  ],
  exports: [RideSearchService, RideService, RideDetailsService],
})
export class RideModule {}
