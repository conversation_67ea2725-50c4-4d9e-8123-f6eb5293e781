import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { LocationPoint } from '@shared/shared/event-emitter';
import { Ride } from '@shared/shared/repositories/models/ride.model';
import {
  ProductRepository,
  RideLifecycleRepository,
  RideRepository,
} from '@shared/shared/repositories';
import { UserProfileRepository } from '@shared/shared/repositories/user-profile.repository';
// import { NotificationService } from '@shared/shared/common/notifications/engagespot/engagespot.service';
// import { AppConfigService } from '@shared/shared/config';
import { RabbitMQEventPublisher } from '@shared/shared/event-emitter/publishers/rabbitmq-event.publisher';
import { DriverVehicleRepository } from '@shared/shared/repositories/driver-vehicle.repository';
import { UserProfileStatus } from '@shared/shared/repositories/models/userProfile.model';
import { RideLifecycle } from '@shared/shared/repositories/models/rideLifecycle.model';
import {
  RideOfferStatus,
  RideStatus,
} from '@shared/shared/modules/ride-matching/constants';
import { FileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';
import { UserMetaDataService } from '../user-meta-data/user-meta-data.service';
import { RideOfferRepository } from '@shared/shared/repositories/ride-offer.repository';
import { GoogleRouteMatrixService } from '@shared/shared/common/google/google-route-matrix.service';
import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';
// import { NotificationService } from '@shared/shared/common/notifications/engagespot/engagespot.service';

export interface CreateRideData {
  riderId: string;
  productId: string;
  pickup: LocationPoint;
  destination: LocationPoint;
  stops?: LocationPoint[] | undefined;
  mode?: 'now' | 'later';
  scheduledFor?: Date;
}

export interface RideListFilters {
  excludeStatuses?: RideStatus[];
  status?: RideStatus;
  page?: number;
  limit?: number;
}

@Injectable()
export class RideService {
  private readonly logger = new Logger(RideService.name);

  constructor(
    private readonly rideRepository: RideRepository,
    private readonly rideLifecycleRepository: RideLifecycleRepository,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly productRepository: ProductRepository,
    // private readonly notificationService: NotificationService,
    // private readonly configService: AppConfigService,
    private readonly rabbitmqEventPublisher: RabbitMQEventPublisher,
    private readonly driverVehicleRepository: DriverVehicleRepository,
    private readonly fileUploadService: FileUploadService,
    private readonly userMetaDataService: UserMetaDataService,
    private readonly rideOfferRepository: RideOfferRepository,
    private readonly googleRouteMatrixService: GoogleRouteMatrixService,
    private readonly h3UtilityService: H3UtilityService,
  ) {}

  /**
   * Create a new ride - OPTIMIZED for high concurrency
   */
  async requestRide(data: CreateRideData): Promise<Ride> {
    const startTime = Date.now();
    this.logger.log(`Requesting ride for rider ${data.riderId}`);

    try {
      const [riderProfile, product, rideExists] = await Promise.all([
        this.userProfileRepository.findById(data.riderId),
        this.productRepository.findById(data.productId),
        this.rideRepository.findUnique({
          where: {
            riderId: data.riderId,
            status: {
              notIn: [
                RideStatus.TRIP_COMPLETED,
                RideStatus.CANCELLED,
                RideStatus.UNASSIGNED,
              ],
            },
          },
        }),
      ]);
      if (rideExists) {
        throw new BadRequestException(
          'You have an active ride. Please complete or cancel it before requesting a new one.',
        );
      }
      if (!riderProfile) {
        throw new NotFoundException(`Rider with ID ${data.riderId} not found`);
      }
      if (!product) {
        throw new NotFoundException(
          `Product with ID ${data.productId} not found`,
        );
      }
      if (riderProfile && !riderProfile?.rideOtp) {
        const rideOtp = this.generateVerificationCode();
        await this.userProfileRepository.updateById(riderProfile.id, {
          rideOtp,
        });
      }
      const rideMode = data.mode || 'now';

      // Calculate distance and duration from pickup to destination with stops

      const { duration, distance } = await this.calculateDistanceAndDuration(
        data.pickup,
        data.destination,
        data.stops,
      );
      console.log({
        message: 'distance and duration in ride request',
        duration,
        distance,
      });
      const rideData: Omit<
        Ride,
        'id' | 'createdAt' | 'updatedAt' | 'deletedAt'
      > = {
        riderId: data.riderId,
        productId: data.productId,
        status: RideStatus.REQUESTED,
        pickupLocation: data.pickup as LocationPoint,
        destinationLocation: data.destination as LocationPoint,
        stops: data.stops || null,
        driverId: null,
        completedAt: null,
        duration,
        distance,
      };
      // console.log(rideData)
      const lifecycleData = {
        status: RideStatus.REQUESTED,
        meta: {
          notes: `Ride ${rideMode === 'later' ? 'scheduled' : 'requested'} by rider`,
          mode: rideMode,
          scheduledFor: data.scheduledFor?.toISOString(),
          processingStartTime: startTime,
        },
      };

      const ride = await this.rideRepository.createRideWithLifecycle(
        rideData,
        lifecycleData,
      );

      // set delay for 10 sec
      // await new Promise((resolve) => setTimeout(resolve, 10000));
      this.publishRideRequestedEventAsync(ride, data, rideMode);

      const processingTime = Date.now() - startTime;
      this.logger.log(
        `Ride ${ride.id} created successfully in ${processingTime}ms (mode: ${rideMode})`,
      );

      return ride;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(
        `Failed to create ride for rider ${data.riderId} after ${processingTime}ms:`,
        error,
      );
      throw error;
    }
  }

  async calculateDistanceAndDuration(
    pickup: LocationPoint,
    destination: LocationPoint,
    stops?: LocationPoint[],
  ): Promise<{ duration: number | null; distance: number | null }> {
    try {
      // Convert stops to the format expected by GoogleRouteMatrixService
      const intermediates = stops?.map((stop) => ({
        lat: stop.lat,
        lng: stop.lng,
      }));

      const result =
        await this.googleRouteMatrixService.computeDistanceAndDuration(
          { lat: pickup.lat, lng: pickup.lng },
          { lat: destination.lat, lng: destination.lng },
          intermediates,
        );

      if (!result) {
        this.logger.warn('No route data returned from Google service.');
        return { duration: null, distance: null };
      }

      const durationSeconds = result.duration;
      const distanceMeters = result.distance;

      this.logger.log(
        `Route calculated: ${distanceMeters}m, ${durationSeconds}s${
          stops ? ` with ${stops.length} stops` : ''
        }`,
      );

      return {
        duration: durationSeconds,
        distance: distanceMeters,
      };
    } catch (error: any) {
      this.logger.warn(
        `Failed to calculate route: ${error?.message || 'Unknown error'}`,
      );
      return { duration: null, distance: null };
    }
  }

  private publishRideRequestedEventAsync(
    ride: Ride,
    data: CreateRideData,
    rideMode: string,
  ): void {
    console.log(
      `RIDE_SERVICE: Publishing ride requested event for ride ${ride.id}, mode: ${rideMode}`,
    );
    setTimeout(async () => {
      try {
        const eventData: any = {
          rideId: ride.id,
          riderId: data.riderId,
          productId: data.productId,
          pickupLocation: data.pickup,
          destinationLocation: data.destination,
          stops: data.stops || [],
          distance: ride.distance,
          duration: ride.duration,
          requestedAt: new Date().toISOString(),
          mode: rideMode as 'now' | 'later',
          ...(data.scheduledFor && {
            scheduledFor: data.scheduledFor.toISOString(),
          }),
        };

        // Only publish immediately for 'now' rides
        if (rideMode === 'now') {
          this.logger.log(
            ` RIDE_SERVICE: About to publish IMMEDIATE ride ${ride.id} to RabbitMQ`,
          );
          await this.rabbitmqEventPublisher.publishRideRequested(eventData);
          this.logger.log(
            ` RIDE_SERVICE: Successfully published ride requested event for immediate ride ${ride.id}`,
          );
        } else {
          this.logger.log(
            `RIDE_SERVICE: Scheduled ride ${ride.id} created, will be processed at ${data.scheduledFor} - NOT PUBLISHING NOW`,
          );
        }
      } catch (error) {
        this.logger.error(
          `RIDE_SERVICE: Failed to publish ride requested event for ride ${ride.id}:`,
          error,
        );
        // Consider adding to a retry queue here
      }
    }, 0);
  }

  /**
   * Get rides for a rider
   */
  async getRiderRides(
    riderId: string,
    filters?: RideListFilters,
  ): Promise<Ride[]> {
    this.logger.log(`Getting rides for rider ${riderId}`);

    // Validate rider exists
    const rider = await this.userProfileRepository.findById(riderId);
    if (!rider) {
      throw new NotFoundException(`Rider with ID ${riderId} not found`);
    }

    const excludeStatuses = filters?.excludeStatuses || [
      RideStatus.TRIP_COMPLETED,
      RideStatus.CANCELLED,
    ];

    return this.rideRepository.findRidesByRiderId(riderId, excludeStatuses);
  }

  /**
   * Get ride details by ID
   */
  async getRideById(rideId: string): Promise<Ride> {
    this.logger.log(`Getting ride details for ID ${rideId}`);

    const ride = await this.rideRepository.findRideByIdWithDetailsRider(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }
    if (ride.driver?.profilePictureUrl) {
      ride['driver']['profilePictureUrl'] =
        await this.fileUploadService.getSignedUrl(
          ride.driver?.profilePictureUrl,
          3600, // 1 hour expiry
        );
    }
    if (ride.product?.icon) {
      ride['product']['icon'] = await this.fileUploadService.getSignedUrl(
        ride.product?.icon,
        3600, // 1 hour expiry
      );
    }
    return ride;
  }

  /**
   * Accept ride by driver
   */
  async acceptRide(
    rideId: string,
    driverId: string,
    driverVehicleId?: string,
  ): Promise<Ride> {
    this.logger.log(`Driver ${driverId} accepting ride ${rideId}`);

    // Validate ride exists
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // // Check if ride already has a driver
    if (ride.driverId) {
      throw new BadRequestException(
        `Ride has already been accepted by another driver`,
      );
    }

    // // Check if ride is in correct status to be accepted
    if (ride.status !== RideStatus.REQUESTED) {
      throw new BadRequestException(
        `Cannot accept ride with status ${ride.status}. Only requested rides can be accepted.`,
      );
    }

    const rideOffer = await this.rideOfferRepository.findRideOfferByStatus(
      ride.id,
      RideOfferStatus.ACCEPTED,
    );
    if (rideOffer) {
      throw new BadRequestException(`Ride already accepted by another driver`);
    }
    // Validate driver exists
    const driver = await this.userProfileRepository.findById(driverId, {
      include: { metaData: true },
    });
    if (!driver) {
      throw new NotFoundException(`Driver with ID ${driverId} not found`);
    }

    // Ensure driver profile is active before accepting ride
    if (driver.status !== UserProfileStatus.ACTIVE) {
      throw new BadRequestException(
        'Your account is not active. Please complete onboarding steps to activate your account before accepting rides.',
      );
    }

    // Determine driverVehicleId: use provided or fallback to driver's primary vehicle
    let resolvedDriverVehicleId = driverVehicleId;
    if (!resolvedDriverVehicleId) {
      const primaryVehicle = await this.driverVehicleRepository.findMany({
        where: { userProfileId: driverId, isPrimary: true },
        take: 1,
      });
      resolvedDriverVehicleId = primaryVehicle[0]?.id;
    }

    if (!resolvedDriverVehicleId) {
      throw new BadRequestException(
        'Driver vehicle not found. Please set a primary vehicle or provide driverVehicleId.',
      );
    }

    // Ensure driver profile is active before accepting ride
    if (driver.status !== 'active') {
      throw new BadRequestException(
        'Your account is not active. Please complete onboarding steps to activate your account before accepting rides.',
      );
    }

    // Update ride with driver, driverVehicle and status
    const updatedRide = await this.rideRepository.updateById(rideId, {
      driverId,
      driverVehicleId: resolvedDriverVehicleId,
      status: RideStatus.ACCEPTED,
    });

    // Create lifecycle entry
    await this.rideLifecycleRepository.createRideLifecycle({
      rideId,
      status: RideStatus.ACCEPTED,
      driverId,
    });

    // Prepare matched event data for RabbitMQ
    const matchedEventData = {
      rideId,
      riderId: ride.riderId,
      driverId,
      offerId: `offer_${rideId}_${driverId}_${Date.now()}`, // Generate a unique offer ID
      acceptedAt: new Date().toISOString(),
    };
    //     const rideDetails = await this.rideRepository.findRideByIdWithDetailsById(rideId);
    // console.log('Ride details after acceptance:', rideDetails);
    // Publish ride accepted event to RabbitMQ
    try {
      await this.rabbitmqEventPublisher.publishRideMatched(matchedEventData);
      this.logger.log(`Published ride matched event for ride ${rideId}`);
    } catch (error) {
      this.logger.error(
        `Failed to publish ride matched event for ride ${rideId}:`,
        error,
      );
      // Don't fail the acceptance if event publishing fails
    }

    // Fetch the updated ride with rider details
    const rideWithRiderDetails =
      await this.rideRepository.findRideByIdWithRiderDetails(rideId);
    if (!rideWithRiderDetails) {
      this.logger.error(
        `Failed to fetch ride with rider details for ride ${rideId}`,
      );
      return updatedRide; // Fallback to basic ride data
    }

    // Convert rider profile picture URL to full S3 URL if needed
    if (rideWithRiderDetails.rider?.profilePictureUrl) {
      rideWithRiderDetails.rider.profilePictureUrl =
        await this.fileUploadService.getSignedUrl(
          rideWithRiderDetails.rider.profilePictureUrl,
          3600, // 1 hour expiry
        );
    }

    this.logger.log(`Ride ${rideId} accepted by driver ${driverId}`);
    return rideWithRiderDetails;
  }

  /**
   * Start ride by driver with OTP verification
   */
  async startRide(
    rideId: string,
    driverId: string,
    otp: string,
  ): Promise<Ride> {
    this.logger.log(
      `Driver ${driverId} starting ride ${rideId} with OTP ${otp}`,
    );

    // Validate ride exists
    const ride = await this.rideRepository.findRideByIdWithDetailsRider(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // Check if ride is assigned to the particular driver
    if (ride.driverId !== driverId) {
      throw new BadRequestException(
        `Ride ${rideId} is not assigned to driver ${driverId}`,
      );
    }

    // Check if ride is in correct status to be started
    if (ride.status !== RideStatus.ACCEPTED) {
      throw new BadRequestException(
        `Cannot start ride with status ${ride.status}. Only accepted rides can be started.`,
      );
    }

    // Verify OTP with rider profile rideOtp
    if (!ride.rider?.rideOtp) {
      throw new BadRequestException(
        'No OTP found for this ride. Please contact support.',
      );
    }

    if (ride.rider.rideOtp !== otp) {
      throw new BadRequestException(
        'Invalid OTP. Please verify the OTP with the rider.',
      );
    }

    // Update ride status to PROCESSING and set otpVerifiedAt
    const updatedRide = await this.rideRepository.updateRideStatus(
      rideId,
      RideStatus.TRIP_STARTED,
      {
        otpVerifiedAt: new Date(),
      },
    );

    // Create lifecycle entry for ride started
    await this.rideLifecycleRepository.createRideLifecycle({
      rideId: ride.id,
      status: RideStatus.TRIP_STARTED,
      location: ride.pickupLocation,
      driverId,
      meta: {
        notes: 'Ride started by driver with OTP verification',
        verifiedAt: new Date().toISOString(),
      },
    });

    // Prepare ride started event data for RabbitMQ
    const startedEventData = {
      rideId,
      riderId: ride.riderId,
      driverId,
      driver: ride.driver,
      startedAt: new Date().toISOString(),
      pickupLocation: {
        latitude: ride.pickupLocation?.lat || 0,
        longitude: ride.pickupLocation?.lng || 0,
      },
    };

    // Publish ride started event to RabbitMQ
    try {
      await this.rabbitmqEventPublisher.publishRideStarted(startedEventData);
      this.logger.log(`Published ride started event for ride ${rideId}`);
    } catch (error) {
      this.logger.error(
        `Failed to publish ride started event for ride ${rideId}:`,
        error,
      );
      // Don't fail the ride start if event publishing fails
    }

    this.logger.log(
      `Ride ${rideId} started successfully by driver ${driverId}`,
    );
    return updatedRide;
  }

  /**
   * Cancel ride by rider
   * Status changes to CANCELLED
   */
  async cancelRideByRider(
    rideId: string,
    riderId: string,
    reason?: string,
  ): Promise<Ride> {
    this.logger.log(`Rider ${riderId} cancelling ride ${rideId}`);

    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // Validate rider owns this ride
    if (ride.riderId !== riderId) {
      throw new BadRequestException('You can only cancel your own rides');
    }

    if (
      ride.status === RideStatus.TRIP_COMPLETED ||
      ride.status === RideStatus.CANCELLED ||
      ride.status === RideStatus.TRIP_STARTED
    ) {
      throw new BadRequestException(
        `Cannot cancel ride with status ${ride.status}`,
      );
    }
    const driverId = ride.driverId || null;
    // console.log(driverId)
    // Update ride status to CANCELLED
    const updatedRide = await this.rideRepository.updateById(rideId, {
      status: RideStatus.CANCELLED,
    });

    // Create lifecycle entry
    await this.rideLifecycleRepository.createRideLifecycle({
      rideId,
      status: RideStatus.CANCELLED,
      driverId,
      meta: {
        cancellationReason: reason || 'Cancelled by rider',
        cancelledBy: riderId,
        cancelledByType: 'rider',
        notes: `Ride cancelled by rider: ${reason || 'No reason provided'}`,
      },
    });

    // Publish ride cancelled event
    try {
      const cancelledEventData: any = {
        rideId,
        riderId,
        driverId,
        cancelledBy: 'rider',
        reason: reason || 'Cancelled by rider',
        cancelledAt: new Date().toISOString(),
      };

      await this.rabbitmqEventPublisher.publishRideCancelled(
        cancelledEventData,
      );
      this.logger.log(`Published ride cancelled event for ride ${rideId}`);
    } catch (error) {
      this.logger.error(
        `Failed to publish ride cancelled event for ride ${rideId}:`,
        error,
      );
      // Don't fail the cancellation if event publishing fails
    }

    this.logger.log(`Ride ${rideId} cancelled by rider ${riderId}`);
    return updatedRide;
  }

  /**
   * Cancel ride by driver
   * Status changes to PROCESSING (to allow reassignment)
   */
  async cancelRideByDriver(
    rideId: string,
    driverId: string,
    reason?: string,
  ): Promise<Ride> {
    this.logger.log(`Driver ${driverId} cancelling ride ${rideId}`);

    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    if (ride.driverId !== driverId) {
      throw new BadRequestException(
        'You can only cancel rides assigned to you',
      );
    }

    if (
      ride.status === RideStatus.TRIP_COMPLETED ||
      ride.status === RideStatus.CANCELLED ||
      ride.status === RideStatus.TRIP_STARTED
    ) {
      throw new BadRequestException(
        `Cannot cancel ride with status ${ride.status}`,
      );
    }

    const updatedRide = await this.rideRepository.updateById(rideId, {
      status: RideStatus.REQUESTED,
      driverId: null,
    });

    // Create lifecycle entry
    await this.rideLifecycleRepository.createRideLifecycle({
      rideId,
      status: RideStatus.CANCELLED,
      driverId,
      meta: {
        cancellationReason: reason || 'Cancelled by driver',
        cancelledBy: driverId,
        cancelledByType: 'driver',
        notes: `Ride cancelled by driver: ${reason || 'No reason provided'}`,
      },
    });

    try {
      const cancelledEventData: any = {
        rideId,
        riderId: ride.riderId,
        driverId,
        cancelledBy: 'driver',
        reason: reason || 'Cancelled by driver',
        cancelledAt: new Date().toISOString(),
      };

      await this.rabbitmqEventPublisher.publishRideCancelled(
        cancelledEventData,
      );
      this.logger.log(`Published ride cancelled event for ride ${rideId}`);

      // Trigger ride rematching by publishing ride requested event
      const rematchEventData: any = {
        rideId,
        riderId: ride.riderId,
        productId: ride.productId,
        pickupLocation: ride.pickupLocation,
        destinationLocation: ride.destinationLocation,
        stops: ride.stops || [],
        requestedAt: new Date().toISOString(),
        mode: 'now',
        isRematch: true,
        originalDriverId: driverId,
      };

      await this.rabbitmqEventPublisher.publishRideRequested(rematchEventData);
      this.logger.log(
        `Published ride rematch event for ride ${rideId} after driver cancellation`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to publish ride events for ride ${rideId}:`,
        error,
      );
      // Don't fail the cancellation if event publishing fails
    }

    this.logger.log(
      `Ride ${rideId} cancelled by driver ${driverId}, status changed to PROCESSING for rematching`,
    );
    return updatedRide;
  }

  /**
   * Get rides for driver with status filtering
   */
  async getRidesForDriver(
    driverId: string,
    filters?: RideListFilters,
  ): Promise<Ride[]> {
    this.logger.log(
      `Getting rides for driver ${driverId} with status filter: ${filters}`,
    );

    // Validate driver exists
    const driver = await this.userProfileRepository.findById(driverId);
    if (!driver) {
      throw new NotFoundException(`Driver with ID ${driverId} not found`);
    }

    const excludeStatuses = filters?.excludeStatuses || [
      RideStatus.TRIP_COMPLETED,
      RideStatus.CANCELLED,
    ];

    return this.rideRepository.findRidesByDriverId(driverId, excludeStatuses);
  }

  /**
   * Get ride lifecycle history
   */
  async getRideLifecycle(rideId: string): Promise<RideLifecycle[]> {
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    return this.rideLifecycleRepository.findLifecyclesByRideId(rideId);
  }

  /**
   * End ride by driver
   * - Optionally replace destination with newDestination
   * - If replaced, push previous destination as a stop
   * - Change ride status to TRIP_COMPLETED, set completedAt, and add lifecycle entry
   */
  async endRide(
    rideId: string,
    driverId: string,
    newDestination?: LocationPoint,
  ): Promise<Ride> {
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    if (ride.driverId !== driverId) {
      throw new BadRequestException('You can only end rides assigned to you');
    }

    if (ride.status !== RideStatus.TRIP_STARTED) {
      throw new BadRequestException(
        `Cannot end ride with status ${ride.status}. Only started rides can be ended.`,
      );
    }

    let updatedStops = ride.stops || [];
    let newDestinationLocation = ride.destinationLocation;

    // If a new destination is provided, push previous destination into stops and replace
    if (newDestination) {
      if (ride.destinationLocation) {
        updatedStops = [...(ride.stops || []), ride.destinationLocation];
      }
      newDestinationLocation = newDestination;
    }
    const durationInseconds =
      ride.otpVerifiedAt
        ? Math.floor((new Date().getTime() - ride.otpVerifiedAt.getTime()) / 1000)
        : ride.duration || 0;
    console.log({ durationInseconds });

    const updatedRide = await this.rideRepository.updateById(rideId, {
      status: RideStatus.TRIP_COMPLETED,
      duration: durationInseconds,
      destinationLocation: newDestinationLocation || ride.destinationLocation,
      stops: updatedStops,
      completedAt: new Date(),
    });

    // Lifecycle entry for completion
    await this.rideLifecycleRepository.createRideLifecycle({
      rideId,
      status: RideStatus.TRIP_COMPLETED,
      location: newDestinationLocation,
      driverId,
      meta: {
        notes: newDestination
          ? 'Ride completed with updated destination'
          : 'Ride completed',
        completedAt: new Date().toISOString(),
      },
    });

    // Update rides completed count for both rider and driver
    if (ride.driverId) {
      this.userMetaDataService
        .handleRideCompletion(ride.riderId, ride.driverId)
        .catch((error) => {
          this.logger.error(
            `Failed to update metadata after ride completion: ${error.message}`,
          );
        });
    }

    // Prepare ride completed event data for RabbitMQ
    const driver = await this.userProfileRepository.findById(driverId);
    const completedEventData = {
      rideId,
      riderId: ride.riderId,
      driverId,
      driver: driver,
      completedAt: new Date().toISOString(),
      actualFare: 0, // TODO: Calculate actual fare
      distance: ride.distance ?? 0, // TODO: Calculate actual distance
      duration: ride.duration ?? 0, // TODO: Calculate actual duration
    };

    // Publish ride completed event to RabbitMQ
    try {
      await this.rabbitmqEventPublisher.publishRideCompleted(
        completedEventData,
      );
      this.logger.log(`Published ride completed event for ride ${rideId}`);
    } catch (error) {
      this.logger.error(
        `Failed to publish ride completed event for ride ${rideId}:`,
        error,
      );
      // Don't fail the ride completion if event publishing fails
    }

    this.logger.log(`Ride ${rideId} completed successfully`);
    return updatedRide;
  }

  /**
   * Generate verification code
   */
  private generateVerificationCode(): string {
    return Math.floor(1000 + Math.random() * 9000).toString();
  }

  /**
   * Mark destination as reached when driver arrives at a stop
   * @param rideId - The ride ID
   * @param driverId - The driver ID
   * @param location - Current location coordinates
   * @returns Updated ride
   */
  async markDestinationReached(
    rideId: string,
    driverId: string,
    location: { lat: number; lng: number },
  ): Promise<Ride> {
    this.logger.log(
      `Marking destination reached for ride ${rideId} at location (${location.lat}, ${location.lng})`,
    );

    // Fetch the ride with stops
    const ride = await this.rideRepository.findById(rideId);

    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // Verify the driver
    if (ride.driverId !== driverId) {
      throw new BadRequestException(
        'You are not authorized to update this ride',
      );
    }

    // Verify ride status
    if (ride.status !== RideStatus.TRIP_STARTED) {
      throw new BadRequestException(
        `Cannot mark destination reached. Ride status is ${ride.status}`,
      );
    }

    // Check if ride has stops
    if (!ride.stops || !Array.isArray(ride.stops) || ride.stops.length === 0) {
      throw new BadRequestException('This ride has no stops defined');
    }

    // Convert current location to H3 index at resolution 9
    const currentH3 = this.h3UtilityService.coordinatesToH3Index(
      location.lat,
      location.lng,
      9,
    );

    this.logger.log(`Current location H3 index: ${currentH3}`);

    // Convert all stops to H3 indices and check for match
    let matchedStop: any = null;
    let matchedStopIndex = -1;

    for (let i = 0; i < ride.stops.length; i++) {
      const stop = ride.stops[i] as any;
      const stopH3 = this.h3UtilityService.coordinatesToH3Index(
        stop.lat,
        stop.lng,
        9,
      );

      this.logger.log(`Stop ${i} H3 index: ${stopH3}`);

      if (stopH3 === currentH3) {
        matchedStop = stop;
        matchedStopIndex = i;
        break;
      }
    }

    // If no stop matches, throw error
    if (!matchedStop) {
      throw new BadRequestException(
        'Current location does not match any stop in the ride',
      );
    }

    this.logger.log(
      `Matched stop at index ${matchedStopIndex}: ${matchedStop.address || 'No address'}`,
    );

    // // Update ride status to DESTINATION_REACHED
    // const updatedRide = await this.rideRepository.updateById(rideId, {
    //   status: RideStatus.DESTINATION_REACHED,
    // });
    // const rideCycleExists= await this.rideLifecycleRepository.findUnique(
    //       {where:{
    //         rideId,
    //         location: matchedStop,
    //         status: RideStatus.DESTINATION_REACHED
    //       }}
    //     );
    //     if(rideCycleExists){
    //       this.logger.log(
    //       `Ride lifecycle for destination reached at stop index ${matchedStopIndex} already exists. Skipping creation.`,
    //     );
    //       // return ride;
    //     }
    // Create lifecycle entry
    await this.rideLifecycleRepository.createRideLifecycle({
      rideId,
      status: RideStatus.DESTINATION_REACHED,
      location: matchedStop,
      driverId,
      meta: {
        notes: 'Driver reached destination stop',
        stopIndex: matchedStopIndex,
        stopAddress: matchedStop.address || 'Unknown address',
        reachedAt: new Date().toISOString(),
      },
    });

    // Publish destination reached event to RabbitMQ
    const destinationReachedData = {
      rideId,
      riderId: ride.riderId,
      driverId,
      stopAddress: matchedStop.address || 'Unknown address',
      reachedAt: new Date().toISOString(),
    };

    try {
      await this.rabbitmqEventPublisher.publishDestinationReached(
        destinationReachedData,
      );
      this.logger.log(`Published destination reached event for ride ${rideId}`);
    } catch (error) {
      this.logger.error(
        `Failed to publish destination reached event for ride ${rideId}:`,
        error,
      );
    }

    this.logger.log(
      `Successfully marked destination reached for ride ${rideId}`,
    );

    return ride;
  }
}
