import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { TaxGroupService } from '@shared/shared/modules/tax-group/tax-group.service';
import {
  CreateTaxGroupDto,
  UpdateTaxGroupDto,
  TaxGroupResponseDto,
  TaxGroupPaginationDto,
} from './dto';
import {
  ApiResponseDto,
  PaginatedResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('Tax Groups')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('tax-groups')
export class TaxGroupsController {
  constructor(private readonly taxGroupService: TaxGroupService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new tax group' })
  @ApiResponse({
    status: 201,
    description: 'Tax group created successfully',
    type: ApiResponseDto<TaxGroupResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or duplicate name',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async create(@Body() createTaxGroupDto: CreateTaxGroupDto) {
    const data = await this.taxGroupService.createTaxGroup(createTaxGroupDto);
    return {
      success: true,
      message: 'Tax group added successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all tax groups without pagination' })
  @ApiResponse({
    status: 200,
    description: 'All tax groups retrieved successfully',
    type: ApiResponseDto<TaxGroupResponseDto[]>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAll() {
    const data = await this.taxGroupService.findAllTaxGroups();
    return {
      success: true,
      message: 'Tax groups retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('paginated')
  @ApiOperation({ summary: 'Get paginated tax groups with search' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search by name or description',
    example: 'GST',
  })
  @ApiResponse({
    status: 200,
    description: 'Paginated tax groups retrieved successfully',
    type: PaginatedResponseDto<TaxGroupResponseDto>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findPaginated(@Query() paginationDto: TaxGroupPaginationDto) {
    const result = await this.taxGroupService.paginateTaxGroups(paginationDto);
    return {
      success: true,
      message: 'Tax groups retrieved successfully',
      data: result.data,
      meta: result.meta,
      timestamp: Date.now(),
    };
  }

  @Get('active')
  @ApiOperation({ summary: 'Get all active tax groups' })
  @ApiResponse({
    status: 200,
    description: 'Active tax groups retrieved successfully',
    type: ApiResponseDto<TaxGroupResponseDto[]>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findActive() {
    const data = await this.taxGroupService.getActiveTaxGroups();
    return {
      success: true,
      message: 'Active tax groups retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a single tax group by ID' })
  @ApiParam({
    name: 'id',
    description: 'Tax group ID',
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Tax group retrieved successfully',
    type: ApiResponseDto<TaxGroupResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'Tax group not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    const data = await this.taxGroupService.findTaxGroupById(id);
    return {
      success: true,
      message: 'Tax group retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a tax group' })
  @ApiParam({
    name: 'id',
    description: 'Tax group ID',
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Tax group updated successfully',
    type: ApiResponseDto<TaxGroupResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or duplicate name',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Tax group not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateTaxGroupDto: UpdateTaxGroupDto,
  ) {
    const data = await this.taxGroupService.updateTaxGroup(
      id,
      updateTaxGroupDto,
    );
    return {
      success: true,
      message: 'Tax group updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a tax group' })
  @ApiParam({
    name: 'id',
    description: 'Tax group ID',
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Tax group deleted successfully',
    type: ApiResponseDto<TaxGroupResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Tax group is in use and cannot be deleted',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Tax group not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    const data = await this.taxGroupService.deleteTaxGroup(id);
    return {
      success: true,
      message: 'Tax group deleted successfully',
      data,
      timestamp: Date.now(),
    };
  }
}
