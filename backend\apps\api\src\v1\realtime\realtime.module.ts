import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { RealtimeGateway } from './realtime.gateway';
import { RealtimeService } from './realtime.service';
import { RealtimeConnectionStateService } from './realtime-connection-state.service';
import { WebSocketEventConsumerService } from './websocket-event-consumer.service';
import { GlobalEventEmitterModule } from '@shared/shared/event-emitter/event-emitter.module';
import { UserProfileRepository } from '@shared/shared/repositories/user-profile.repository';
import { PrismaModule } from '@shared/shared/database/prisma/prisma.module';
import { RedisModule } from '@shared/shared/database/redis/redis.module';
import { AppConfigModule, AppConfigService } from '@shared/shared/config';
import { WsJwtAuthGuard, WsTokenManagerService } from '@shared/shared';

@Module({
  imports: [
    AppConfigModule,
    GlobalEventEmitterModule,
    PrismaModule, // For database access
    RedisModule, // For Redis-based state management and Socket.IO adapter
    JwtModule.register({}), // JWT module for token verification

    // RabbitMQ for consuming WebSocket events
    RabbitMQModule.forRootAsync({
      imports: [AppConfigModule],
      useFactory: (config: AppConfigService) => ({
        exchanges: [
          {
            name: 'websocket.notifications',
            type: 'topic',
            options: { durable: true },
          },
        ],
        uri: config.rabbitmqUrl,
        connectionInitOptions: {
          wait: false,
          timeout: 10000,
        },
        connectionManagerOptions: {
          heartbeatIntervalInSeconds: 60,
          reconnectTimeInSeconds: 5,
        },
        enableControllerDiscovery: true,
      }),
      inject: [AppConfigService],
    }),
  ],
  providers: [
    RealtimeGateway,
    RealtimeService,
    RealtimeConnectionStateService, // Redis-based connection state management
    WebSocketEventConsumerService,
    WsJwtAuthGuard,
    WsTokenManagerService,

    UserProfileRepository, // For user profile database operations
    {
      provide: 'IRealtimeGateway',
      useExisting: RealtimeGateway,
    },
  ],
  exports: [
    RealtimeGateway,
    RealtimeService,
    RealtimeConnectionStateService,
    WebSocketEventConsumerService,
  ],
})
export class RealtimeModule {}
