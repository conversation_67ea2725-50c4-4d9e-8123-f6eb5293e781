import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BatchOrchestratorService } from './batch-orchestrator.service';
import { AlgorithmService } from '../../algorithm/src';
import { RequestBuilderService } from '../helpers/request-builder.service';
import { RideData } from '../interfaces';
import {
  RideExecutionStatus,
  ServiceResult,
  EnhancedDriverSearchResult,
  RideMatchingSystemConfig,
  PenaltyConfiguration,
} from '../interfaces/enhanced-ride-matching.interface';
import { PenaltyType } from '../interfaces/shared.interface';
import { RadiusExpansionOrchestratorService } from './radius-expansion-orchestrator.service';
import { BatchStateManagerService } from './batch-state-manager.service';
import { DriverPenaltyService } from './driver-penalty.service';
import { OverlapPreventionService } from './overlap-prevention.service';
import { DriverRankingService } from './driver-ranking.service';
// import { UserProfileRepository } from '@shared/shared/repositories/user-profile.repository';
import { RideRepository } from '@shared/shared/repositories/ride.repository';
import { RideMode, RideStatus } from '../constants';
import { Event } from '@shared/shared/common/constants/constants';
import { RideStatusUpdatedDto } from '@shared/shared/common/events/ride-status.update.event';
import { v4 as uuidv4 } from 'uuid';

export interface IDriverProfile {
  firstName: string;
  lastName: string;
  profilePic: string | null;
  driverRating: number;
  verificationCode: number;
  vehicleRegNumber: string;
  vehicleColor: string;
  vehicleModel: string;
  vehicleClass: string;
}

@Injectable()
export class MainOrchestratorService {
  private readonly logger = new Logger(MainOrchestratorService.name);

  // Default system configuration
  private readonly DEFAULT_CONFIG: RideMatchingSystemConfig = {
    driverFinder: {
      enableFixedRadiusMode: true,
      defaultSearchMode: 'fixed',
      batchSizeLimit: 20,
      maxConcurrentSearches: 10,
    },
    radiusExpansion: {
      initialRadius: 2,
      radiusSteps: [2, 4, 6, 8, 10, 12, 15],
      maxRadius: 15,
      stepDelayMs: 10000,
      enableFallbackExpansion: false,
    },
    penalties: {
      batchNonResponsePenalty: 5,
      recentRejectionPenalty: 3,
      cancellationPenalty: 10,
      noShowPenalty: 15,
      penaltyDecayRate: 0.1,
      penaltyTtlHours: 24,
      maxPenaltyScore: 50,
      rejectionTimeWindowHours: 6,
    },
    batchProcessing: {
      batchSize: 10,
      batchTimeoutMs: 10000,
      maxBatches: 5,
      enableRadiusExpansion: true,
      maxRadius: 15,
      batchDelayMs: 0, // No delay by default
      overlapPrevention: true,
      penaltyAwareRanking: true,
      radiusExpansionConfig: {
        initialRadius: 2,
        radiusSteps: [2, 4, 6, 8, 10, 12, 15],
        maxRadius: 15,
        stepDelayMs: 10000,
        enableFallbackExpansion: false,
      },
      penaltyConfig: {
        batchNonResponsePenalty: 5,
        recentRejectionPenalty: 3,
        cancellationPenalty: 10,
        noShowPenalty: 15,
        penaltyDecayRate: 0.1,
        penaltyTtlHours: 24,
        maxPenaltyScore: 50,
        rejectionTimeWindowHours: 6,
      },
      stateManagement: {
        persistState: true,
        stateTtlMs: 3600000, // 1 hour
        enableRecovery: true,
        cleanupExpiredStates: true,
      },
    },
  };

  constructor(
    private readonly algorithmService: AlgorithmService,
    private readonly batchOrchestrator: BatchOrchestratorService,
    private readonly requestBuilderService: RequestBuilderService,
    private readonly radiusExpansionOrchestrator: RadiusExpansionOrchestratorService,
    private readonly driverPenaltyService: DriverPenaltyService,
    private readonly batchStateManager: BatchStateManagerService,
    private readonly overlapPreventionService: OverlapPreventionService,
    private readonly driverRankingService: DriverRankingService,
    private readonly eventEmitter: EventEmitter2,
    // private readonly userProfileRepo: UserProfileRepository,
    private readonly rideRepository: RideRepository,
    @InjectQueue('ride-processing') private readonly rideProcessingQueue: Queue,
  ) {}

  /**
   * Enhanced ride matching process with queue-based orchestration
   * Process initial ride request - find drivers and queue first batch
   * PUBLIC: Called by queue processor
   */
  async processInitialRideRequest(
    request: RideData,
    config: RideMatchingSystemConfig,
    correlationId: string,
  ): Promise<void> {
    try {
      const event: RideStatusUpdatedDto = {
        rideId: request.rideId,
        status: RideStatus.PROCESSING,
        message: 'Your Ride is processing',
        riderId: request.riderId,
        timestamp: new Date().toISOString(),
        metadata: {
          correlationId: correlationId,
        },
      };
      this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, event);

      if (!config) {
        this.logger.warn(
          `Config is undefined for initial ride processing of ride ${request.rideId}, using default configuration`,
        );
        config = this.DEFAULT_CONFIG;
      }

      // Ensure all required config sections exist
      const safeConfig: RideMatchingSystemConfig = {
        ...this.DEFAULT_CONFIG,
        ...config,
        radiusExpansion: {
          ...this.DEFAULT_CONFIG.radiusExpansion,
          ...(config.radiusExpansion || {}),
        },
        penalties: {
          ...this.DEFAULT_CONFIG.penalties,
          ...(config.penalties || {}),
        },
        batchProcessing: {
          ...this.DEFAULT_CONFIG.batchProcessing,
          ...(config.batchProcessing || {}),
        },
      };

      this.logger.log(
        `Processing initial ride request for ride ${request.rideId}`,
      );

      // Initialize ride state if it doesn't exist
      this.logger.debug(
        `[STATE_FIX] Initializing ride state for new ride ${request.rideId}`,
      );
      let currentState = await this.batchStateManager.getRideState(
        request.rideId,
      );
      if (!currentState) {
        const initResult = await this.batchStateManager.initializeRideState(
          request.rideId,
          safeConfig?.radiusExpansion?.initialRadius || 2,
          safeConfig?.radiusExpansion?.maxRadius || 15,
          { correlationId, source: 'initial_request' },
        );
        if (!initResult.success || !initResult.data) {
          this.logger.error(
            `[STATE_FIX] Failed to initialize ride state for ${request.rideId}: ${initResult.error || 'No data returned'}`,
          );
          await this.markRideAsFailed(
            request.rideId,
            `State initialization failed: ${initResult.error || 'Unknown error'}`,
          );
          return;
        }
        this.logger.log(
          `[STATE_FIX] Successfully initialized ride state for ${request.rideId}`,
        );
        currentState = initResult.data;
      } else {
        this.logger.debug(
          `[STATE_FIX] Ride state already exists for ${request.rideId}, status: ${currentState.status}`,
        );
      }

      if (currentState && this.shouldTerminateProcessing(currentState.status)) {
        this.logger.log(
          `Terminating initial processing for ride ${request.rideId} - status: ${currentState.status}`,
        );
        return;
      }
      const initialRadius = safeConfig?.radiusExpansion?.initialRadius || 2;

      // Build driver search request
      const driverSearchRequest =
        await this.requestBuilderService.buildDriverSearchRequest(request);

      // Search for drivers at initial radius
      const searchResult = await this.searchDriversWithPenaltyFiltering(
        request,
        driverSearchRequest,
        initialRadius,
        safeConfig,
      );

      if (!searchResult.success || !searchResult.data) {
        this.logger.warn(
          `Initial driver search failed for ride ${request.rideId} at ${initialRadius}km`,
        );
        await this.queueRadiusExpansion(
          request,
          initialRadius,
          0,
          safeConfig,
          correlationId,
        );
        return;
      }

      const enhancedSearchResult = searchResult.data;

      this.logger.log(
        `INITIAL_DRIVERS_FOUND: Ride ${request.rideId} - Found ${enhancedSearchResult?.drivers?.length || 0} drivers ` +
          `within ${initialRadius}km radius`,
      );

      // If no drivers found, expand radius
      if (
        !enhancedSearchResult?.drivers ||
        enhancedSearchResult.drivers.length === 0
      ) {
        this.logger.warn(
          `INITIAL_NO_DRIVERS: Ride ${request.rideId} - No drivers found within ${initialRadius}km radius ` +
            `(searchMetadata: totalCandidates=${enhancedSearchResult?.searchMetadata?.totalCandidates || 0}, ` +
            `penaltyFiltered=${enhancedSearchResult?.searchMetadata?.penaltyFiltered || 0})`,
        );
        await this.queueRadiusExpansion(
          request,
          initialRadius,
          0,
          safeConfig,
          correlationId,
        );
        return;
      }

      // Get driver groups from algorithm
      const algorithmRequestData =
        await this.requestBuilderService.buildAlgorithmRequestData(
          request,
          enhancedSearchResult,
        );

      const algorithmResult =
        await this.algorithmService.execute(algorithmRequestData);

      if (!algorithmResult.success || algorithmResult.groups.length === 0) {
        this.logger.error(
          `ALGORITHM_BATCHING_FAILED: Ride ${request.rideId} - Algorithm returned no groups ` +
            `(success=${algorithmResult.success}, groups=${algorithmResult.groups?.length || 0}, ` +
            `totalDrivers=${enhancedSearchResult?.drivers?.length || 0}) - forcing radius expansion`,
        );

        // DIAGNOSTIC: Log driver details if algorithm fails
        if (
          enhancedSearchResult?.drivers &&
          enhancedSearchResult.drivers.length > 0
        ) {
          this.logger.warn(
            `ALGORITHM_INPUT_DRIVERS: Ride ${request.rideId} - ${enhancedSearchResult.drivers.length} drivers ` +
              `available but algorithm produced 0 groups. Sample: ` +
              `[${enhancedSearchResult.drivers
                .slice(0, 3)
                .map((d) => `${d.driverId}:${d.ranking?.toFixed(1)}`)
                .join(', ')}]`,
          );
        }

        await this.queueRadiusExpansion(
          request,
          initialRadius,
          0,
          safeConfig,
          correlationId,
        );
        return;
      }

      // Apply batch deduplication and validation
      const deduplicatedGroups = await this.deduplicateAndValidateBatches(
        request.rideId,
        algorithmResult.groups,
        initialRadius,
        0, // initial expansion
        correlationId,
      );

      // Queue first batch notification job immediately with deduplicated groups
      await this.queueBatchNotification(
        request,
        deduplicatedGroups,
        0, // First batch
        initialRadius,
        safeConfig,
        0, // Initial expansion count
        correlationId,
      );
    } catch (error: any) {
      this.logger.error(
        `Initial ride processing failed for ride ${request.rideId}:`,
        error,
      );
      await this.markRideAsFailed(
        request.rideId,
        `Initial processing failed: ${error.message}`,
      );
    }
  }

  /**
   * Process batch notification job
   * PUBLIC: Called by queue processor
   */
  async processBatchNotification(
    request: RideData,
    driverGroups: any[][],
    batchIndex: number,
    radius: number,
    config: RideMatchingSystemConfig,
    expansionCount: number,
    correlationId: string,
  ): Promise<void> {
    try {
      // Status Check First - Early Termination
      const currentState = await this.batchStateManager.getRideState(
        request.rideId,
      );
      if (this.shouldTerminateProcessing(currentState?.status)) {
        this.logger.log(
          `Terminating batch ${batchIndex + 1} processing for ride ${request.rideId} - status: ${currentState?.status}`,
        );
        return;
      }

      if (batchIndex >= driverGroups.length) {
        // All batches processed, expand radius
        this.logger.log(
          `All batches processed for ride ${request.rideId}, queueing radius expansion`,
        );
        await this.queueRadiusExpansion(
          request,
          radius,
          expansionCount,
          config,
          correlationId,
        );
        return;
      }

      const batchNumber = batchIndex + 1;
      const batchDrivers = driverGroups[batchIndex];
      const batchId = `${request.rideId}-batch-${batchNumber}`;

      this.logger.log(
        `Processing batch notification ${batchNumber}/${driverGroups.length} ` +
          `for ride ${request.rideId} with ${batchDrivers.length} drivers`,
      );

      // Emit batch processing started event
      this.eventEmitter.emit('ride.batch.processing.started', {
        rideId: request.rideId,
        riderId: request.riderId,
        timestamp: new Date().toISOString(),
        batchNumber: batchNumber,
        totalBatches: driverGroups.length,
        driverCount: batchDrivers.length,
        batchId: batchId,
        correlationId,
      });

      // Overlap Prevention
      const overlapResult =
        await this.overlapPreventionService.preventBatchOverlap(
          request.rideId,
          batchId,
          batchDrivers,
        );

      const finalBatchDrivers = [
        ...overlapResult.allowedDrivers,
        ...overlapResult.penalizedRetries,
      ];

      if (finalBatchDrivers.length === 0) {
        this.logger.log(
          `Batch ${batchNumber} eliminated due to overlap prevention, queueing next batch`,
        );
        await this.queueBatchNotification(
          request,
          driverGroups,
          batchIndex + 1,
          radius,
          config,
          expansionCount,
          correlationId,
        );
        return;
      }

      // Apply penalty adjustments
      const batchAdjustedDrivers =
        await this.driverRankingService.applyBatchPenaltyAdjustments(
          finalBatchDrivers,
          batchNumber,
          radius,
        );

      // Create batch state
      const batchStateResult = await this.batchStateManager.createBatchState(
        request.rideId,
        batchNumber,
        batchAdjustedDrivers.map((d) => d.driverId),
        radius,
        config.batchProcessing.batchTimeoutMs,
        {
          overlapPrevention: overlapResult.preventionMetadata,
          batchId,
          correlationId,
        },
      );

      if (!batchStateResult.success) {
        this.logger.error(
          `Failed to create batch state for batch ${batchNumber}: ${batchStateResult.error}`,
        );
        await this.queueBatchNotification(
          request,
          driverGroups,
          batchIndex + 1,
          radius,
          config,
          expansionCount,
          correlationId,
        );
        return;
      }

      // Offer Creation & Event Emission
      await this.batchOrchestrator.processBatchAsync(
        request.rideId,
        request.riderId,
        batchAdjustedDrivers,
        batchNumber,
        driverGroups.length,
        request,
        config.batchProcessing,
        async (accepted: boolean, acceptedDriverId?: string) => {
          if (accepted && acceptedDriverId) {
            // Handle acceptance
            await this.handleBatchAcceptanceAsync(
              request.rideId,
              batchId,
              acceptedDriverId,
              batchAdjustedDrivers,
              correlationId,
            );
          } else {
            // Handle timeout/rejection - queue next batch with delay
            await this.handleBatchTimeoutAsync(
              request,
              driverGroups,
              batchIndex,
              batchId,
              batchAdjustedDrivers,
              radius,
              config,
              expansionCount,
              correlationId,
            );
          }
        },
      );
    } catch (error) {
      this.logger.error(
        `Batch notification processing failed for ride ${request.rideId} batch ${batchIndex + 1}:`,
        error,
      );
      await this.queueBatchNotification(
        request,
        driverGroups,
        batchIndex + 1,
        radius,
        config,
        expansionCount,
        correlationId,
      );
    }
  }

  /**
   * Process radius expansion job
   * PUBLIC: Called by queue processor
   */
  async processRadiusExpansion(
    request: RideData,
    currentRadius: number,
    expansionCount: number,
    config: RideMatchingSystemConfig,
    correlationId: string,
  ): Promise<void> {
    try {
      // Defensive check: ensure config is properly defined
      if (!config || !config.radiusExpansion) {
        this.logger.warn(
          `Config or radiusExpansion is undefined for radius expansion of ride ${request.rideId}, using default configuration`,
        );
        config = this.DEFAULT_CONFIG;
      }

      // Ensure radiusExpansion config exists with all required properties
      const radiusConfig = {
        ...this.DEFAULT_CONFIG.radiusExpansion,
        ...(config.radiusExpansion || {}),
      };

      // Check ride status first - terminate if accepted/cancelled
      this.logger.warn(
        `[STATE_TRACE] About to getRideState for ${request.rideId} in radius expansion (currentRadius: ${currentRadius}km)`,
      );
      const currentState = await this.batchStateManager.getRideState(
        request.rideId,
      );

      this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, <RideStatusUpdatedDto>{
        rideId: request.rideId,
        status: RideStatus.SEARCHING_DRIVERS,
        message: 'Searching drivers',
        timestamp: new Date().toISOString(),
        metadata: {
          currentRadius,
          expansionCount,
          correlationId: correlationId || uuidv4(),
        },
      });

      if (
        this.shouldTerminateProcessing(currentState?.status) ||
        !currentState
      ) {
        this.logger.log(
          `Terminating radius expansion for ride ${request.rideId} - status: ${currentState?.status}`,
        );
        return;
      }

      // Declare variables for radius expansion result
      let nextRadius: number | null = null;
      let newExpansionCount: number | null = null;
      let shouldTerminateExpansion = false;

      // Calculate next radius from configured steps with diagnostic logging
      const radiusSteps = radiusConfig.radiusSteps;
      const currentStepIndex = radiusSteps.indexOf(currentRadius);

      // DIAGNOSTIC LOG: Validate radius expansion calculation
      this.logger.warn(
        `RADIUS_EXPANSION_DIAGNOSIS: Ride ${request.rideId} - currentRadius=${currentRadius}, ` +
          `radiusSteps=[${radiusSteps.join(', ')}], currentStepIndex=${currentStepIndex}, ` +
          `radiusConfig.maxRadius=${radiusConfig.maxRadius}`,
      );

      if (currentStepIndex === -1) {
        this.logger.error(
          `RADIUS_EXPANSION_INDEX_MISMATCH: Ride ${request.rideId} - currentRadius ${currentRadius}km ` +
            `not found in configured steps [${radiusSteps.join(', ')}] - using fallback logic`,
        );

        // Fallback: find next larger radius in steps
        const fallbackIndex = radiusSteps.findIndex(
          (step) => step > currentRadius,
        );
        if (
          fallbackIndex !== -1 &&
          radiusSteps[fallbackIndex] <= radiusConfig.maxRadius
        ) {
          nextRadius = radiusSteps[fallbackIndex];
          newExpansionCount = expansionCount + 1;
          this.logger.log(
            `RADIUS_EXPANSION_FALLBACK_SUCCESS: Ride ${request.rideId} - using fallback radius ` +
              `${nextRadius}km (step ${fallbackIndex + 1}/${radiusSteps.length}, expansion ${newExpansionCount})`,
          );
        } else {
          this.logger.error(
            `RADIUS_EXPANSION_FALLBACK_FAILED: Ride ${request.rideId} - no suitable larger radius found ` +
              `(current=${currentRadius}km, available steps=[${radiusSteps.filter((s) => s > currentRadius).join(', ')}], ` +
              `maxRadius=${radiusConfig.maxRadius}km) - terminating expansion`,
          );
          shouldTerminateExpansion = true;
        }
      } else {
        const nextStepIndex = currentStepIndex + 1;

        // DIAGNOSTIC LOG: Valid step index found
        this.logger.debug(
          `RADIUS_EXPANSION_VALID_STEP: Ride ${request.rideId} - currentStepIndex=${currentStepIndex}, ` +
            `nextStepIndex=${nextStepIndex}, nextRadius=${radiusSteps[nextStepIndex]}km`,
        );

        // Check if next radius exceeds maxRadius
        if (
          nextStepIndex >= radiusSteps.length ||
          radiusSteps[nextStepIndex] > radiusConfig.maxRadius
        ) {
          this.logger.log(
            `Max radius reached for ride ${request.rideId}. Current: ${currentRadius}km, Max: ${radiusConfig.maxRadius}km, ` +
              `Steps exhausted: ${nextStepIndex} >= ${radiusSteps.length}`,
          );
          shouldTerminateExpansion = true;
        } else {
          nextRadius = radiusSteps[nextStepIndex];
          newExpansionCount = expansionCount + 1;
          this.logger.log(
            `Expanding radius for ride ${request.rideId} from ${currentRadius}km to ${nextRadius}km ` +
              `(expansion ${newExpansionCount}, step ${nextStepIndex + 1}/${radiusSteps.length})`,
          );
        }
      }

      // Early termination if no valid next radius found
      if (shouldTerminateExpansion || !nextRadius || !newExpansionCount) {
        await this.handleMaxRadiusReached(request.rideId, currentRadius);
        return;
      }

      // Update ride state with new radius
      this.logger.debug(
        `[STATE_TRACE] About to updateRideState for ${request.rideId} to radius ${nextRadius}km, expansion ${newExpansionCount}`,
      );
      const updateResult = await this.batchStateManager.updateRideState(
        request.rideId,
        {
          status: RideExecutionStatus.SEARCHING_DRIVERS,
          currentRadius: nextRadius,
          expansionCount: newExpansionCount,
        },
      );
      this.logger.debug(
        `[STATE_TRACE] updateRideState result for ${request.rideId}: ${updateResult.success ? 'SUCCESS' : 'FAILED'}${updateResult.success ? '' : `, error: ${updateResult.error}`}`,
      );
      if (!updateResult.success) {
        this.logger.error(
          `[STATE_TRACE] Failed to update ride state during expansion for ${request.rideId}: ${updateResult.error}`,
        );
      }

      // Build driver search request
      const driverSearchRequest =
        await this.requestBuilderService.buildDriverSearchRequest(request);

      // Search for drivers in new radius
      const searchResult = await this.searchDriversWithPenaltyFiltering(
        request,
        driverSearchRequest,
        nextRadius,
        config,
      );

      if (!searchResult.success || !searchResult.data) {
        this.logger.warn(
          `RADIUS_EXPANSION_SEARCH_FAILED: Ride ${request.rideId} - Driver search failed at radius ${nextRadius}km ` +
            `(expansion ${newExpansionCount}) - Error: ${searchResult.error || 'Unknown error'}`,
        );
        await this.queueRadiusExpansion(
          request,
          nextRadius,
          newExpansionCount,
          config,
          correlationId,
        );
        return;
      }

      const enhancedSearchResult = searchResult.data;

      this.logger.log(
        `RADIUS_EXPANSION_DRIVERS_FOUND: Ride ${request.rideId} - Found ${enhancedSearchResult?.drivers?.length || 0} drivers ` +
          `within ${nextRadius}km radius (expansion ${newExpansionCount}) - ` +
          `Metadata: totalCandidates=${enhancedSearchResult.searchMetadata?.totalCandidates || 0}, ` +
          `penaltyFiltered=${enhancedSearchResult.searchMetadata?.penaltyFiltered || 0}`,
      );

      // If no drivers found, schedule next expansion
      if (
        !enhancedSearchResult?.drivers ||
        enhancedSearchResult.drivers.length === 0
      ) {
        this.logger.warn(
          `RADIUS_EXPANSION_NO_DRIVERS: Ride ${request.rideId} - No eligible drivers found at radius ${nextRadius}km ` +
            `(expansion ${newExpansionCount}) - totalCandidates=${enhancedSearchResult.searchMetadata?.totalCandidates || 0}, ` +
            `penaltyFiltered=${enhancedSearchResult.searchMetadata?.penaltyFiltered || 0} - scheduling next expansion`,
        );
        await this.queueRadiusExpansion(
          request,
          nextRadius,
          newExpansionCount,
          config,
          correlationId,
        );
        return;
      }

      // Get driver groups from algorithm
      const algorithmRequestData =
        await this.requestBuilderService.buildAlgorithmRequestData(
          request,
          enhancedSearchResult,
        );

      const algorithmResult =
        await this.algorithmService.execute(algorithmRequestData);

      if (!algorithmResult.success || algorithmResult.groups.length === 0) {
        this.logger.error(
          `RADIUS_EXPANSION_ALGORITHM_FAILED: Ride ${request.rideId} - Algorithm failed for radius ${nextRadius}km ` +
            `(expansion ${newExpansionCount}) - success=${algorithmResult.success}, groups=${algorithmResult.groups?.length || 0}, ` +
            `availableDrivers=${enhancedSearchResult.drivers?.length || 0}, reason=${algorithmResult.reason || 'Unknown'}`,
        );

        // DIAGNOSTIC: Log if drivers available but algorithm failed
        if (
          enhancedSearchResult.drivers &&
          enhancedSearchResult.drivers.length > 0
        ) {
          this.logger.warn(
            `RADIUS_EXPANSION_ALGORITHM_INPUT: Ride ${request.rideId} - ${enhancedSearchResult.drivers.length} drivers ` +
              `available at ${nextRadius}km but algorithm produced 0 groups. Sample rankings: ` +
              `[${enhancedSearchResult.drivers
                .slice(0, 5)
                .map(
                  (d) =>
                    `${d.driverId}:rank=${d.ranking?.toFixed(1)},eta=${d.etaMinutes}m`,
                )
                .join('; ')}]`,
          );
        }

        await this.queueRadiusExpansion(
          request,
          nextRadius,
          newExpansionCount,
          config,
          correlationId,
        );
        return;
      }

      // Validate batch composition for the new radius
      const totalBatches = algorithmResult.groups.length;
      const totalDriversInBatches = algorithmResult.groups.reduce(
        (sum, group) => sum + group.length,
        0,
      );
      const emptyBatches = algorithmResult.groups.filter(
        (group) => group.length === 0,
      ).length;

      this.logger.debug(
        `RADIUS_EXPANSION_BATCH_COMPOSITION: Ride ${request.rideId} - Created ${totalBatches} batches ` +
          `at ${nextRadius}km (expansion ${newExpansionCount}) with ${totalDriversInBatches} total drivers ` +
          `(avg ${(totalDriversInBatches / totalBatches || 0).toFixed(1)} per batch, emptyBatches=${emptyBatches})`,
      );

      // Check for duplicate drivers across batches at this radius
      const allDriverIds = algorithmResult.groups.flatMap((group) =>
        group.map((d) => d.driverId),
      );
      const uniqueDriverIds = new Set(allDriverIds);
      const duplicateCount = allDriverIds.length - uniqueDriverIds.size;

      if (duplicateCount > 0) {
        this.logger.error(
          `RADIUS_EXPANSION_BATCH_DUPLICATES: Ride ${request.rideId} - Found ${duplicateCount} duplicate drivers ` +
            `in ${totalBatches} batches at ${nextRadius}km (unique=${uniqueDriverIds.size}/${allDriverIds.length}) - ` +
            `Duplicate IDs: [${Array.from(allDriverIds)
              .filter((id, index, arr) => arr.indexOf(id) !== index)
              .slice(0, 10)
              .join(', ')}...]`,
        );

        // Log batch sizes for debugging
        algorithmResult.groups.forEach((group, index) => {
          if (group.length > 0) {
            const sampleIds = group.slice(0, 3).map((d) => d.driverId);
            this.logger.debug(
              `RADIUS_EXPANSION_BATCH_${index + 1}: Ride ${request.rideId} - ${group.length} drivers at ${nextRadius}km: [${sampleIds.join(', ')}...]`,
            );
          } else {
            this.logger.warn(
              `RADIUS_EXPANSION_EMPTY_BATCH: Ride ${request.rideId} - Batch ${index + 1} is empty at radius ${nextRadius}km`,
            );
          }
        });
      }

      // Queue first batch job for new radius
      await this.queueBatchNotification(
        request,
        algorithmResult.groups,
        0, // First batch of new radius
        nextRadius,
        config,
        newExpansionCount,
        correlationId,
      );

      this.logger.log(
        `RADIUS_EXPANSION_BATCH_QUEUED: Ride ${request.rideId} - Queued first batch (${algorithmResult.groups[0]?.length || 0} drivers) ` +
          `for radius ${nextRadius}km (expansion ${newExpansionCount}, totalBatches=${totalBatches})`,
      );
    } catch (error: any) {
      this.logger.error(
        `Radius expansion failed for ride ${request.rideId}:`,
        error,
      );
      await this.markRideAsFailed(
        request.rideId,
        `Radius expansion failed: ${error.message}`,
      );
    }
  }

  /**
   * Queue batch notification job
   */
  private async queueBatchNotification(
    request: RideData,
    driverGroups: any[][],
    batchIndex: number,
    radius: number,
    config: RideMatchingSystemConfig,
    expansionCount: number,
    correlationId: string,
  ): Promise<void> {
    const delay = batchIndex === 0 ? 0 : 10000; // No delay for first batch, 10s for subsequent

    await this.rideProcessingQueue.add(
      'batch-notification',
      {
        rideData: request,
        driverGroups,
        batchIndex,
        radius,
        config,
        expansionCount,
        correlationId,
      },
      {
        delay,
        attempts: 3,
        removeOnComplete: 10,
        removeOnFail: 50,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    );

    this.logger.log(
      `Queued batch notification ${batchIndex + 1} for ride ${request.rideId} ` +
        `(delay: ${delay}ms, radius: ${radius}km)`,
    );
  }

  /**
   * Queue radius expansion job
   */
  private async queueRadiusExpansion(
    request: RideData,
    currentRadius: number,
    expansionCount: number,
    config: RideMatchingSystemConfig,
    correlationId: string,
  ): Promise<void> {
    // Defensive check for config
    const safeConfig = config || this.DEFAULT_CONFIG;
    const delay = safeConfig.radiusExpansion?.stepDelayMs || 10000;

    await this.rideProcessingQueue.add(
      'radius-expansion',
      {
        rideData: request,
        currentRadius,
        expansionCount,
        config: safeConfig,
        correlationId,
      },
      {
        delay,
        attempts: 3,
        removeOnComplete: 10,
        removeOnFail: 50,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    );

    this.logger.log(
      `Queued radius expansion for ride ${request.rideId} ` +
        `from ${currentRadius}km (delay: ${delay}ms)`,
    );
  }

  /**
   * Check if processing should terminate based on ride status
   */
  private shouldTerminateProcessing(status?: RideExecutionStatus): boolean {
    return (
      status === RideExecutionStatus.ACCEPTED ||
      status === RideExecutionStatus.CANCELLED ||
      status === RideExecutionStatus.FAILED ||
      status === RideExecutionStatus.UNASSIGNED
    );
  }

  /**
   * Handle batch acceptance
   */
  private async handleBatchAcceptanceAsync(
    rideId: string,
    batchId: string,
    acceptedDriverId: string,
    _batchDrivers: any[],
    correlationId: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `BATCH_ACCEPTED: Driver ${acceptedDriverId} accepted ride ${rideId} in batch ${batchId}`,
      );

      // IMMEDIATE CLEANUP - Prevent race conditions
      await this.performImmediateCleanup(rideId, acceptedDriverId);

      // Update ride state to accepted
      await this.batchStateManager.updateRideState(rideId, {
        status: RideExecutionStatus.ACCEPTED,
        acceptedDriverId,
        acceptedAt: new Date().toISOString(),
      });

      // Mark batch as accepted
      await this.batchStateManager.markBatchAccepted(
        rideId,
        batchId,
        acceptedDriverId,
      );

      // Emit ride accepted event
      this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, <RideStatusUpdatedDto>{
        rideId,
        status: RideStatus.ACCEPTED,
        message: 'Driver found and accepted',
        timestamp: new Date().toISOString(),
        metadata: {
          acceptedDriverId,
          batchId,
          correlationId,
        },
      });

      this.logger.log(
        `Successfully handled batch acceptance for ride ${rideId} by driver ${acceptedDriverId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling batch acceptance for ride ${rideId}:`,
        error,
      );
    }
  }

  /**
   * Handle batch timeout - queue next batch with delay
   */
  private async handleBatchTimeoutAsync(
    request: RideData,
    driverGroups: any[][],
    currentBatchIndex: number,
    batchId: string,
    batchDrivers: any[],
    radius: number,
    config: RideMatchingSystemConfig,
    expansionCount: number,
    correlationId: string,
  ): Promise<void> {
    try {
      // Mark batch as expired
      await this.batchStateManager.markBatchExpired(
        request.rideId,
        batchId,
        batchDrivers.map((d) => d.driverId),
      );

      // Apply penalties
      await this.applyBatchNonResponsePenalties(
        batchDrivers.map((d) => d.driverId),
        request.rideId,
        batchId,
        currentBatchIndex + 1,
        radius,
        config.penalties,
      );

      // Queue next batch with 10-second delay
      await this.queueBatchNotification(
        request,
        driverGroups,
        currentBatchIndex + 1,
        radius,
        config,
        expansionCount,
        correlationId,
      );

      this.logger.log(
        `Handled batch timeout for ride ${request.rideId}, queued next batch with delay`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling batch timeout for ride ${request.rideId}:`,
        error,
      );
    }
  }

  /**
   * Handle max radius reached
   */
  private async handleMaxRadiusReached(
    rideId: string,
    finalRadius: number,
  ): Promise<void> {
    try {
      this.logger.log(
        `Max radius reached for ride ${rideId} at ${finalRadius}km - marking as unassigned`,
      );

      // Update ride state to unassigned
      await this.batchStateManager.updateRideState(rideId, {
        status: RideExecutionStatus.UNASSIGNED,
        finalRadius,
        completedAt: new Date().toISOString(),
      });

      // Cancel any pending offers

      await Promise.all([
        this.batchOrchestrator.cancelRideOffers(rideId),
        this.rideRepository.updateRideStatus(rideId, RideStatus.UNASSIGNED),
      ]);

      // Emit ride unassigned event
      this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, <RideStatusUpdatedDto>{
        rideId,
        status: RideStatus.UNASSIGNED,
        message: 'No drivers available within maximum search radius',
        timestamp: new Date().toISOString(),
        metadata: {
          finalRadius,
          maxRadius: this.DEFAULT_CONFIG.radiusExpansion.maxRadius,
          correlationId: uuidv4(),
        },
      });

      this.logger.log(
        `Successfully marked ride ${rideId} as unassigned after reaching max radius`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling max radius reached for ride ${rideId}:`,
        error,
      );
    }
  }

  /**
   * Mark ride as failed
   */
  private async markRideAsFailed(
    rideId: string,
    reason: string,
  ): Promise<void> {
    try {
      await this.batchStateManager.updateRideState(rideId, {
        status: RideExecutionStatus.FAILED,
        failureReason: reason,
        completedAt: new Date().toISOString(),
      });

      // Cancel any pending offers
      await this.batchOrchestrator.cancelRideOffers(rideId);

      // Emit ride failed event
      this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, <RideStatusUpdatedDto>{
        rideId,
        status: RideStatus.CANCELLED,
        message: 'Ride processing failed',
        timestamp: new Date().toISOString(),
        metadata: {
          correlationId: uuidv4(),
          reason,
        },
      });

      this.logger.error(`Marked ride ${rideId} as failed: ${reason}`);
    } catch (error) {
      this.logger.error(`Error marking ride ${rideId} as failed:`, error);
    }
  }

  /**
   * Search drivers with penalty filtering and overlap prevention
   */
  private async searchDriversWithPenaltyFiltering(
    request: RideData,
    driverSearchRequest: any,
    radius: number,
    config: RideMatchingSystemConfig,
  ): Promise<ServiceResult<EnhancedDriverSearchResult>> {
    try {
      // Defensive check for config
      const safeConfig = config || this.DEFAULT_CONFIG;
      const radiusConfig =
        safeConfig.radiusExpansion || this.DEFAULT_CONFIG.radiusExpansion;
      const penaltyConfig =
        safeConfig.penalties || this.DEFAULT_CONFIG.penalties;

      // Update ride state
      await this.batchStateManager.updateRideState(request.rideId, {
        status: RideExecutionStatus.SEARCHING_DRIVERS,
        currentRadius: radius,
      });

      // Search for drivers with fixed radius
      const searchResult =
        await this.radiusExpansionOrchestrator.executeRadiusStep(
          request.rideId,
          driverSearchRequest,
          radius,
          radiusConfig,
        );

      if (!searchResult.success || !searchResult.data) {
        return {
          success: false,
          error: searchResult.error || 'Driver search failed',
        };
      }

      const drivers = searchResult.data?.drivers || [];

      // Apply penalty filtering
      const penaltyFilterResult =
        await this.driverRankingService.filterDriversByPenalties(
          drivers,
          this.driverRankingService.calculateDynamicPenaltyThreshold(
            drivers.length,
          ),
          penaltyConfig,
        );

      if (!penaltyFilterResult.success) {
        this.logger.warn(
          `Penalty filtering failed for ride ${request.rideId}, proceeding with all drivers`,
        );
      }

      const eligibleDrivers = penaltyFilterResult.success
        ? penaltyFilterResult.data?.eligibleDrivers || []
        : drivers;

      // Apply penalty-based ranking adjustments
      const rankingResult =
        await this.driverRankingService.applyPenaltyRankings(
          eligibleDrivers,
          request.rideId,
          config.penalties,
        );

      const finalDrivers = rankingResult.success
        ? rankingResult.data || []
        : eligibleDrivers;

      return {
        success: true,
        data: {
          drivers: finalDrivers,
          searchMetadata: {
            totalCandidates: drivers.length,
            eligibleAfterFiltering: eligibleDrivers.length,
            penaltyFiltered: drivers.length - eligibleDrivers.length,
            finalSelected: finalDrivers.length,
            searchTimeMs: searchResult.data?.processingTimeMs || 0,
            overlapFiltered: 0,
          },
          fixedRadius: 2,
          radiusExpansionDisabled: true,
          penaltyAdjustedCount: finalDrivers.length,
          overlapPreventedCount: 0,
          expansionCount: 0,
          searchRadius: radius,
          totalFound: drivers.length,
          processingTimeMs: searchResult.data?.processingTimeMs || 0,
        },
      };
    } catch (error) {
      this.logger.error(
        `Driver search with penalty filtering failed for ride ${request.rideId}:`,
        error,
      );
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Apply batch non-response penalties to drivers
   */
  private async applyBatchNonResponsePenalties(
    driverIds: string[],
    rideId: string,
    batchId: string,
    batchNumber: number,
    radius: number,
    _penaltyConfig: PenaltyConfiguration,
  ): Promise<void> {
    try {
      this.logger.log(
        `Applying batch non-response penalties to ${driverIds.length} drivers ` +
          `for ride ${rideId} batch ${batchNumber}`,
      );

      const penaltyPromises = driverIds.map(async (driverId) => {
        try {
          await this.driverPenaltyService.applyPenalty(
            driverId,
            rideId,
            PenaltyType.BATCH_NON_RESPONSE,
            'non batch response',
            {
              batchId,
              batchNumber,
              radiusKm: radius,
            },
          );
        } catch (error) {
          this.logger.error(
            `Failed to apply penalty to driver ${driverId} for batch ${batchId}:`,
            error,
          );
        }
      });

      await Promise.allSettled(penaltyPromises);

      this.logger.log(
        `Completed applying batch non-response penalties for ride ${rideId} batch ${batchNumber}`,
      );
    } catch (error) {
      this.logger.error(
        `Error applying batch non-response penalties for ride ${rideId}:`,
        error,
      );
    }
  }

  /**
   * Get driver profile and vehicle information
   */
  async getDriverProfileAndVehicle(rideId: string) {
    const ride = await this.rideRepository.findRideByIdWithDetailsById(rideId);

    const driverProfile = ride?.driver;
    const riderProfile = ride?.rider;
    const driverVehicle = ride?.driverVehicle || null;

    if (!driverProfile || !riderProfile) {
      this.logger.warn(
        `Ride with ${rideId} is not having a profile so returning unknown entity`,
      );
      const driver = {
        firstName: 'unknown',
        lastName: 'unknown',
        profilePic: 'unknown',
        driverRating: 0.1,
        vehicleRegNumber: 'TUXI-TEST-0001',
        vehicleColor: 'RED',
        vehicleModel: 'TUXI-TEST VEHICLE',
        vehicleClass: 'TUXI-TEST-CLASS',
      };
      const rider = {
        firstName: 'unknown',
        lastName: 'unknown',
        profilePic: 'unknown',
        verificationCode: 1234,
      };
      return {
        driver,
        rider,
      };
    }

    const driver: IDriverProfile = {
      firstName: driverProfile?.firstName,
      lastName: driverProfile.lastName,
      profilePic: driverProfile?.profilePictureUrl || null,
      driverRating: Number(driverProfile?.metaData?.avgRating) || 0.0,
      verificationCode: Number(riderProfile?.rideOtp),
      vehicleRegNumber: driverVehicle?.vehicleNumber || null,
      vehicleColor: driverVehicle?.vehicleColor || null,
      vehicleModel: driverVehicle?.vehicleType?.name || null,
      vehicleClass: driverVehicle?.vehicleClass || null,
    };

    const rider: any = {
      firstName: riderProfile?.firstName,
      lastName: riderProfile?.lastName,
      profilePic: riderProfile?.profilePictureUrl || null,
      riderRating: 4.6,
    };
    return {
      driver,
      rider,
    };
  }

  /**
   * Handle driver acceptance with enhanced state management
   */
  async handleDriverAcceptance(
    rideId: string,
    driverId: string,
    offerId: string,
    riderId: string,
  ): Promise<boolean> {
    try {
      this.logger.log(
        `Processing enhanced driver acceptance: driver ${driverId} accepted ride ${rideId}`,
      );

      // IMMEDIATE CLEANUP - Prevent race conditions
      await this.performImmediateCleanup(rideId, driverId);

      // Update ride state to accepted
      await this.batchStateManager.updateRideState(rideId, {
        status: RideExecutionStatus.ACCEPTED,
        acceptedDriverId: driverId,
        acceptedAt: new Date().toISOString(),
      });

      // Emit ride accepted event to resolve waiting batch
      this.eventEmitter.emit('ride.accepted', { rideId, driverId });

      // Emit driver accepted event
      const result = await this.getDriverProfileAndVehicle(rideId);
      console.log('Driver acceptance event payload:', {
        rideId,
        riderId,
        driverId,
        result,
      });
      this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, <RideStatusUpdatedDto>{
        rideId: rideId,
        riderId: riderId,
        driverId: driverId,
        status: RideStatus.ACCEPTED,
        message: `${result.driver.firstName} ${result.driver.lastName} Accepted Your Ride Request`,
        timestamp: new Date().toISOString(),
        driver: result.driver,
        metadata: {
          acceptedAt: new Date().toISOString(),
          totalProcessingTimeMs: 0,
          offerId,
          correlationId: uuidv4(),
        },
      });

      this.logger.log(
        `Successfully handled driver acceptance for ride ${rideId} by driver ${driverId}`,
      );

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to handle driver acceptance for ride ${rideId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Perform immediate cleanup when a ride is accepted
   * Prevents race conditions by stopping all ongoing processing
   */
  private async performImmediateCleanup(
    rideId: string,
    driverId: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `IMMEDIATE_CLEANUP_START: Starting immediate cleanup for ride ${rideId} accepted by driver ${driverId}`,
      );

      // 1. Cancel all pending queue jobs for this ride
      await this.cancelPendingQueueJobs(rideId);

      // 2. Stop active batch processing immediately
      await this.batchOrchestrator.cancelActiveBatch(rideId);

      // 3. Cancel all pending offers for this ride
      await this.batchOrchestrator.cancelRideOffers(rideId);

      // 4. Mark radius expansion as complete (not max radius reached)
      await this.radiusExpansionOrchestrator.markRadiusExpansionComplete(
        rideId,
        false,
      );

      // 5. Clean up all state in parallel for better performance
      await Promise.all([
        this.batchStateManager.cleanupRideStates(rideId),
        this.radiusExpansionOrchestrator.cleanupRadiusState(rideId),
        this.overlapPreventionService.cleanupExpiredInvitations(),
      ]);

      this.logger.log(
        `IMMEDIATE_CLEANUP_COMPLETE: Successfully completed immediate cleanup for ride ${rideId}`,
      );
    } catch (error) {
      this.logger.error(
        `IMMEDIATE_CLEANUP_ERROR: Failed to perform immediate cleanup for ride ${rideId}:`,
        error,
      );
      // Don't throw - acceptance should still proceed even if cleanup partially fails
    }
  }

  /**
   * Cancel all pending queue jobs for a specific ride
   */
  private async cancelPendingQueueJobs(rideId: string): Promise<void> {
    try {
      // Get all jobs from the ride-processing queue
      const waitingJobs = await this.rideProcessingQueue.getWaiting();
      const delayedJobs = await this.rideProcessingQueue.getDelayed();
      console.log('cancelPendingQueueJobs');
      console.log(waitingJobs, delayedJobs);
      let cancelledCount = 0;

      // Cancel waiting jobs
      for (const job of waitingJobs) {
        if (this.isJobForRide(job, rideId)) {
          await job.remove();
          cancelledCount++;
          this.logger.debug(
            `QUEUE_CLEANUP: Cancelled waiting job ${job.id} (${job.name}) for ride ${rideId}`,
          );
        }
      }

      // Cancel delayed jobs
      for (const job of delayedJobs) {
        if (this.isJobForRide(job, rideId)) {
          await job.remove();
          cancelledCount++;
          this.logger.debug(
            `QUEUE_CLEANUP: Cancelled delayed job ${job.id} (${job.name}) for ride ${rideId}`,
          );
        }
      }

      this.logger.log(
        `QUEUE_CLEANUP_COMPLETE: Cancelled ${cancelledCount} pending queue jobs for ride ${rideId}`,
      );
    } catch (error) {
      this.logger.error(
        `QUEUE_CLEANUP_ERROR: Failed to cancel pending queue jobs for ride ${rideId}:`,
        error,
      );
    }
  }

  /**
   * Check if a queue job is for a specific ride
   */
  private isJobForRide(job: any, rideId: string): boolean {
    try {
      const jobData = job.data;

      // Check for batch notification jobs
      if (
        job.name === 'process-batch-notification' &&
        jobData?.rideData?.rideId === rideId
      ) {
        return true;
      }

      // Check for radius expansion jobs
      if (
        job.name === 'process-radius-expansion' &&
        jobData?.rideData?.rideId === rideId
      ) {
        return true;
      }

      // Check for initial processing jobs
      if (
        job.name === 'process-ride-initial' &&
        jobData?.rideData?.rideId === rideId
      ) {
        return true;
      }

      return false;
    } catch (error) {
      this.logger.warn(
        `Failed to check if job ${job.id} is for ride ${rideId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Handle ride cancellation with state cleanup
   */
  async handleRideCancellation(
    rideId: string,
    cancelledBy: 'rider' | 'driver' | 'system',
    reason: string,
    driverId?: string,
  ): Promise<boolean> {
    try {
      this.logger.log(
        `Processing enhanced ride cancellation: ride ${rideId} cancelled by ${cancelledBy}`,
      );

      await this.performCancellationCleanup(rideId, cancelledBy, reason);

      this.eventEmitter.emit(Event.RIDE_STATUS_UPDATED, <RideStatusUpdatedDto>{
        rideId,
        status:
          cancelledBy === 'driver'
            ? RideStatus.CANCELLED_BY_DRIVER
            : RideStatus.CANCELLED_BY_RIDER,
        message: 'Ride cancelled',
        timestamp: new Date().toISOString(),
        metadata: {
          correlationId: uuidv4(),
        },
      });

      if (cancelledBy === 'driver') {
        this.logger.log(
          `Driver cancellation detected for ride ${rideId}, triggering rematch after cleanup`,
        );

        const rideData = await this.getRideDataForRematch(rideId);

        if (rideData) {
          try {
            this.logger.log(
              `Starting rematch process for ride ${rideId} after driver cancellation`,
            );

            this.eventEmitter.emit('driver.ride.cancelled', {
              rideId,
              cancelledBy,
              reason,
              driverId,
            });

            await this.queueRematchJob(
              rideData,
              `rematch_${rideId}_${Date.now()}`,
            );

            this.logger.log(
              `Rematch job queued successfully for ride ${rideId}`,
            );
          } catch (error: any) {
            this.logger.error(
              `Rematch process failed for ride ${rideId}:`,
              error,
            );
            await this.markRideAsFailed(
              rideId,
              `Rematch failed: ${error.message}`,
            );
          }
        } else {
          this.logger.error(
            `Could not retrieve ride data for rematch of ride ${rideId}`,
          );
          await this.markRideAsFailed(
            rideId,
            'Could not retrieve ride data for rematch',
          );
        }
      }

      this.eventEmitter.emit('rider.ride.cancelled', {
        rideId,
        cancelledBy,
        reason,
        driverId,
      });

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to handle enhanced ride cancellation for ride ${rideId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Perform cancellation cleanup - similar to acceptance cleanup but updates status to CANCELLED
   */
  private async performCancellationCleanup(
    rideId: string,
    cancelledBy: 'rider' | 'driver' | 'system',
    reason: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `CANCELLATION_CLEANUP_START: Starting cleanup for ride ${rideId} cancelled by ${cancelledBy}`,
      );

      // 1. Cancel all pending queue jobs for this ride
      await this.cancelPendingQueueJobs(rideId);

      // 2. Stop active batch processing immediately
      await this.batchOrchestrator.cancelActiveBatch(rideId);

      // 3. Cancel all pending offers for this ride
      const cancelledOffersCount =
        await this.batchOrchestrator.cancelRideOffers(rideId);

      // 4. Mark radius expansion as complete (cancelled)
      await this.radiusExpansionOrchestrator.markRadiusExpansionComplete(
        rideId,
        false,
      );
      if (cancelledBy !== 'driver') {
        await this.batchStateManager.updateRideState(rideId, {
          status: RideExecutionStatus.CANCELLED,
          failureReason: `Cancelled by ${cancelledBy}: ${reason}`,
          completedAt: new Date().toISOString(),
        });
      } else {
        // For driver cancellations, keep as PROCESSING for rematch
        await this.batchStateManager.updateRideState(rideId, {
          status: RideExecutionStatus.PENDING,
          failureReason: `Driver cancelled: ${reason}`,
          metadata: {
            driverCancellation: true,
            originalCancellationReason: reason,
            rematchTriggered: true,
          },
        });
      }

      // 6. Clean up all state in parallel for better performance
      await Promise.all([
        this.batchStateManager.cleanupRideStates(rideId),
        this.radiusExpansionOrchestrator.cleanupRadiusState(rideId),
        this.overlapPreventionService.cleanupExpiredInvitations(),
      ]);

      this.logger.log(
        `CANCELLATION_CLEANUP_COMPLETE: Successfully completed cleanup for ride ${rideId} ` +
          `(cancelled ${cancelledOffersCount} offers)`,
      );
    } catch (error) {
      this.logger.error(
        `CANCELLATION_CLEANUP_ERROR: Failed to perform cleanup for ride ${rideId}:`,
        error,
      );
      // Don't throw - cancellation should still proceed even if cleanup partially fails
    }
  }

  /**
   * Queue rematch job for driver-cancelled ride
   */
  private async queueRematchJob(
    rideData: RideData,
    correlationId: string,
  ): Promise<void> {
    try {
      const jobData = {
        rideData,
        config: this.DEFAULT_CONFIG,
        correlationId,
      };

      await this.rideProcessingQueue.add('process-ride-initial', jobData, {
        delay: 0,
        attempts: 3,
        removeOnComplete: 10,
        removeOnFail: 50,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      });

      this.logger.log(
        `Queued rematch job for ride ${rideData.rideId} with correlationId ${correlationId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to queue rematch job for ride ${rideData.rideId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get ride data for rematch from database
   */
  private async getRideDataForRematch(
    rideId: string,
  ): Promise<RideData | null> {
    try {
      const ride = await this.rideRepository.findById(rideId);

      if (!ride) {
        this.logger.error(`Ride ${rideId} not found for rematch`);
        return null;
      }

      // Validate required location data
      if (!ride.pickupLocation || !ride.destinationLocation) {
        this.logger.error(`Ride ${rideId} missing location data for rematch`);
        return null;
      }

      // Convert to RideData format
      const rideData: RideData = {
        rideId: ride.id,
        riderId: ride.riderId,
        productId: ride.productId,
        pickupLocation: {
          lat: ride.pickupLocation.lat,
          lng: ride.pickupLocation.lng,
          address: ride.pickupLocation.address || '',
        },
        destinationLocation: {
          lat: ride.destinationLocation.lat,
          lng: ride.destinationLocation.lng,
          address: ride.destinationLocation.address || '',
        },
        stops:
          ride.stops?.map((stop) => ({
            lat: stop.lat,
            lng: stop.lng,
            address: stop.address || '',
          })) || [],
        requestedAt: ride.createdAt.toISOString(),
        mode: 'now' as RideMode,
      };

      this.logger.log(`Retrieved ride data for rematch: ride ${rideId}`);
      return rideData;
    } catch (error) {
      this.logger.error(
        `Failed to get ride data for rematch of ride ${rideId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Deduplicate drivers across batches and validate composition
   * Ensures no driver appears in multiple batches for the same ride/radius
   */
  private async deduplicateAndValidateBatches(
    rideId: string,
    groups: any[][],
    radius: number,
    expansionCount: number,
    _correlationId: string,
  ): Promise<any[][]> {
    try {
      // Get previously processed drivers for this ride
      const rideState = await this.batchStateManager.getRideState(rideId);
      const previouslyProcessedDrivers = new Set<string>();

      if (
        rideState?.metadata &&
        'processedDrivers' in rideState.metadata &&
        rideState.metadata['processedDrivers']
      ) {
        (rideState.metadata['processedDrivers'] as string[]).forEach(
          (driverId: string) => {
            previouslyProcessedDrivers.add(driverId);
          },
        );
      }

      let totalDeduplicated = 0;
      let totalUniqueKept = 0;
      const finalGroups: any[][] = [];
      const allNewDriverIds: string[] = [];

      // Track duplicates within this radius step
      const radiusDeduplicationMap = new Map<string, number>(); // driverId -> first batch index

      groups.forEach((group, batchIndex) => {
        const deduplicatedGroup: any[] = [];
        const localDuplicates: string[] = [];

        group.forEach((driver) => {
          const driverId = driver.driverId;

          // Check for duplicates within this radius step
          if (radiusDeduplicationMap.has(driverId)) {
            const existingBatch = radiusDeduplicationMap.get(driverId)!;
            localDuplicates.push(
              `${driverId} (already in batch ${existingBatch + 1})`,
            );
            totalDeduplicated++;
            return;
          }

          // Check against previously processed drivers from other radius steps
          if (previouslyProcessedDrivers.has(driverId)) {
            localDuplicates.push(
              `${driverId} (processed in previous expansion)`,
            );
            totalDeduplicated++;
            return;
          }

          // Driver is unique - keep it and track
          deduplicatedGroup.push(driver);
          radiusDeduplicationMap.set(driverId, batchIndex);
          allNewDriverIds.push(driverId);
          totalUniqueKept++;
        });

        finalGroups.push(deduplicatedGroup);

        // Log local duplicates if any
        if (localDuplicates.length > 0) {
          this.logger.warn(
            `BATCH_DEDUPLICATION: Ride ${rideId} - Batch ${batchIndex + 1} at ${radius}km had ` +
              `${localDuplicates.length} duplicates removed: [${localDuplicates.slice(0, 3).join('; ')}${localDuplicates.length > 3 ? '...' : ''}]`,
          );
        }
      });

      // Update ride state with newly processed drivers
      if (allNewDriverIds.length > 0) {
        const currentProcessed =
          rideState?.metadata && 'processedDrivers' in rideState.metadata
            ? (rideState.metadata['processedDrivers'] as string[]) || []
            : [];

        const updatedMetadata = {
          ...rideState?.metadata,
          processedDrivers: [...currentProcessed, ...allNewDriverIds],
        };
        await this.batchStateManager.updateRideState(rideId, {
          metadata: updatedMetadata,
        });
      }

      this.logger.log(
        `BATCH_DEDUPLICATION_SUMMARY: Ride ${rideId} - Processed ${groups.reduce((sum, g) => sum + g.length, 0)} ` +
          `drivers into ${finalGroups.length} batches at ${radius}km (expansion ${expansionCount}) - ` +
          `Deduplicated: ${totalDeduplicated}, Unique kept: ${totalUniqueKept}, ` +
          `Final batches: ${finalGroups.map((g) => g.length).join(',')}`,
      );

      if (totalDeduplicated > 0) {
        this.logger.warn(
          `DEDUPLICATION_IMPACT: Ride ${rideId} - Deduplication removed ${totalDeduplicated} drivers ` +
            `(previously processed: ${previouslyProcessedDrivers.size}) - potential algorithm issue`,
        );
      }

      return finalGroups.filter((group) => group.length > 0); // Remove empty batches
    } catch (error) {
      this.logger.error(
        `BATCH_DEDUPLICATION_FAILED: Ride ${rideId} - Error during deduplication:`,
        error,
      );
      // Return original groups on error to avoid blocking
      return groups;
    }
  }
}
