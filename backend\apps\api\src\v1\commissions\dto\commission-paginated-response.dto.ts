import { ApiProperty } from '@nestjs/swagger';
import { CommissionResponseDto } from './commission-response.dto';

export class CommissionPaginatedResponseDto {
  @ApiProperty({
    example: true,
    description: 'Indicates if the request was successful',
  })
  success!: boolean;

  @ApiProperty({
    example: 'Commissions retrieved successfully',
    description: 'Response message',
  })
  message!: string;

  @ApiProperty({
    description: 'Paginated commission data',
  })
  data!: {
    data: CommissionResponseDto[];
    meta: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  };

  @ApiProperty({
    example: 1696579049000,
    description: 'Response timestamp',
  })
  timestamp!: number;
}
