import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsN<PERSON>ber,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ength,
} from 'class-validator';

export class CreateTaxSubcategoryDto {
  @ApiProperty({
    example: 'CGST',
    description: 'Name of the tax subcategory',
    maxLength: 100,
  })
  @IsString({ message: 'Subcategory name must be a string' })
  @IsNotEmpty({ message: 'Subcategory name is required' })
  @MaxLength(100, { message: 'Subcategory name cannot exceed 100 characters' })
  name!: string;

  @ApiProperty({
    example: 9.0,
    description: 'Percentage for this subcategory (max 2 decimal places)',
    minimum: 0.01,
    maximum: 100,
  })
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: 'Percentage must be a number with at most 2 decimal places' },
  )
  @Min(0.01, { message: 'Percentage must be greater than 0' })
  @Max(100, { message: 'Percentage cannot exceed 100' })
  percentage!: number;
}
