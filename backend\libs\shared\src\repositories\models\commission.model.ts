import { BaseEntity } from '../base.repository';
import { TaxGroup } from './taxGroup.model';

export enum CommissionType {
  PERCENTAGE = 'percentage',
  FLAT = 'flat',
}

export interface Commission extends BaseEntity {
  name: string;
  description?: string | null;
  type: CommissionType;
  percentageValue?: number | null;
  flatValue?: number | null;
  taxGroupId?: string | null;
  taxGroup?: TaxGroup;
}
