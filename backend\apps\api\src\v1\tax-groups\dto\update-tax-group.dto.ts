import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  MaxLength,
  IsOptional,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { UpdateTaxSubcategoryDto } from './update-tax-subcategory.dto';

export class UpdateTaxGroupDto {
  @ApiProperty({
    example: 'GST',
    description: 'Name of the tax group',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tax group name must be a string' })
  @IsNotEmpty({ message: 'Tax group name cannot be empty' })
  @MaxLength(100, { message: 'Tax group name cannot exceed 100 characters' })
  name?: string;

  @ApiProperty({
    example: 'Goods and Services Tax',
    description: 'Description of the tax group',
    maxLength: 500,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tax group description must be a string' })
  @MaxLength(500, {
    message: 'Tax group description cannot exceed 500 characters',
  })
  description?: string;

  @ApiProperty({
    example: [
      { id: 'existing-id', name: 'CGST', percentage: 9.0 },
      { name: 'SGST', percentage: 9.0 },
    ],
    description:
      'Array of tax subcategories (replaces all existing subcategories)',
    type: [UpdateTaxSubcategoryDto],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Subcategories must be an array' })
  @ValidateNested({ each: true })
  @Type(() => UpdateTaxSubcategoryDto)
  subcategories?: UpdateTaxSubcategoryDto[];
}
