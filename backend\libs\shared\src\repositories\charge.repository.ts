import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { BaseRepository } from './base.repository';
import { Charge } from './models/charge.model';

@Injectable()
export class ChargeRepository extends BaseRepository<Charge> {
  protected readonly modelName = 'charge';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Maps a raw database object to a Charge model instance.
   * You can adjust this method as needed to fit your Charge model.
   */
  protected mapToModel(raw: any): Charge {
    return raw as Charge;
  }

  /**
   * Create a new charge record.
   */
  async createCharge(
    data: Omit<Charge, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Charge> {
    return this.create(data);
  }

  /**
   * Find all charges by charge group ID.
   */
  async findChargesByChargeGroupId(chargeGroupId: string): Promise<Charge[]> {
    return this.findMany({
      where: { chargeGroupId },
      include: {
        percentageOfCharge: true,
        taxGroup: {
          where: { deletedAt: null },
        },
        commission: {
          where: { deletedAt: null },
        },
      },
    });
  }

  async findChargeById(id: string): Promise<Charge | null> {
    // First find the charge
    const charge = await this.findOne({
      where: { id },
      include: {
        percentageOfCharge: true,
        taxGroup: {
          where: { deletedAt: null },
        },
        commission: {
          where: { deletedAt: null },
        },
      },
    });

    if (!charge) {
      return null;
    }
    return charge;
  }

  /**
   * Find charge by identifier.
   */
  async findChargeByIdentifier(identifier: string): Promise<Charge | null> {
    return this.findOne({
      where: { identifier },
    });
  }

  /**
   * Update charge by ID and charge group ID.
   * Note: Since charges can now exist independently, this method updates the charge
   * regardless of its group attachments. The chargeGroupId parameter is kept for
   * backward compatibility but is no longer used as a strict filter.
   */
  async updateChargeById(
    id: string,
    data: Partial<Omit<Charge, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>,
  ): Promise<Charge> {
    return this.update({
      where: { id },
      data,
      include: {
        percentageOfCharge: true,
        taxGroup: {
          where: { deletedAt: null },
        },
      },
    });
  }

  /**
   * Check if identifier exists (across all charges).
   */
  async identifierExistsGlobally(
    identifier: string | undefined,
    excludeId?: string,
  ): Promise<boolean> {
    const where: any = {
      identifier: {
        equals: identifier,
        mode: 'insensitive',
      },
    };

    if (excludeId) {
      where.id = { not: excludeId };
    }

    const charge = await this.findOne({ where });
    return !!charge;
  }

  /**
   * Check if identifier exists (legacy method for backward compatibility).
   */
  async identifierExists(
    identifier: string | undefined,
    chargeGroupId?: string,
    excludeId?: string,
  ): Promise<boolean> {
    // If chargeGroupId is provided, use the old logic for backward compatibility
    if (chargeGroupId) {
      const where: any = {
        identifier: {
          equals: identifier,
          mode: 'insensitive',
        },
        chargeGroupId,
      };

      if (excludeId) {
        where.id = { not: excludeId };
      }

      const charge = await this.findOne({ where });
      return !!charge;
    }

    // Otherwise, check globally (new behavior)
    return this.identifierExistsGlobally(identifier, excludeId);
  }

  /**
   * Find charges that reference this charge as percentage base.
   */
  async findChargesReferencingAsPercentage(
    chargeId: string,
  ): Promise<Charge[]> {
    return this.findMany({
      where: { percentageOfChargeId: chargeId },
    });
  }

  /**
   * Get paginated charges with search and filter capabilities.
   */
  async findPaginated(
    page: number = 1,
    limit: number = 10,
    filters?: {
      search?: string;
      chargeType?: string;
      priceModel?: string;
      minAmount?: number;
      maxAmount?: number;
      createdFrom?: string;
      createdTo?: string;
      chargeGroupId?: string;
      includeUnattached?: boolean;
      isCommon?: boolean;
      taxGroupId?: string;
    },
  ): Promise<{
    data: Charge[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  }> {
    const offset = (page - 1) * limit;
    const where: any = {
      deletedAt: null,
    };

    // Build search filters
    if (filters?.search) {
      where.OR = [
        {
          name: {
            contains: filters.search,
            mode: 'insensitive',
          },
        },
        {
          identifier: {
            contains: filters.search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Filter by charge type
    if (filters?.chargeType) {
      where.chargeType = filters.chargeType;
    }

    // Filter by isCommon
    if (filters?.isCommon !== undefined) {
      where.isCommon = filters.isCommon;
    }

    // Filter by price model
    if (filters?.priceModel) {
      where.priceModel = filters.priceModel;
    }

    // Filter by amount range (for flat amount and linear rate price models)
    if (filters?.minAmount !== undefined || filters?.maxAmount !== undefined) {
      const priceConditions: any[] = [];

      if (filters?.minAmount !== undefined) {
        priceConditions.push({
          OR: [
            {
              AND: [
                { priceModel: 'flat_amount' },
                { price: { path: ['amount'], gte: filters.minAmount } },
              ],
            },
            {
              AND: [
                { priceModel: 'linear_rate' },
                { price: { path: ['rate'], gte: filters.minAmount } },
              ],
            },
          ],
        });
      }

      if (filters?.maxAmount !== undefined) {
        priceConditions.push({
          OR: [
            {
              AND: [
                { priceModel: 'flat_amount' },
                { price: { path: ['amount'], lte: filters.maxAmount } },
              ],
            },
            {
              AND: [
                { priceModel: 'linear_rate' },
                { price: { path: ['rate'], lte: filters.maxAmount } },
              ],
            },
          ],
        });
      }

      if (priceConditions.length > 0) {
        where.AND = (where.AND || []).concat(priceConditions);
      }
    }

    // Filter by creation date range
    if (filters?.createdFrom || filters?.createdTo) {
      where.createdAt = {};
      if (filters?.createdFrom) {
        where.createdAt.gte = new Date(filters.createdFrom);
      }
      if (filters?.createdTo) {
        where.createdAt.lte = new Date(filters.createdTo);
      }
    }

    // Filter by tax group
    if (filters?.taxGroupId) {
      where.taxGroupId = filters.taxGroupId;
    }

    const [data, total] = await Promise.all([
      this.model.findMany({
        where,
        skip: offset,
        take: limit,
        include: {
          percentageOfCharge: true,
          chargeGroupCharges: {
            where: { deletedAt: null },
            include: {
              chargeGroup: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          taxGroup: {
            where: { deletedAt: null },
          },
          commission: {
            where: { deletedAt: null },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.model.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    interface PaginatedChargesResult {
      data: Charge[];
      total: number;
      page: number;
      limit: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    }

    return {
      data: data.map((charge: any) => this.mapToModel(charge)),
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    } as PaginatedChargesResult;
  }

  /**
   * Attach a commission to a charge.
   */
  async attachCommission(
    chargeId: string,
    commissionId: string,
  ): Promise<Charge> {
    return this.update({
      where: { id: chargeId },
      data: { commissionId },
      include: {
        percentageOfCharge: true,
        taxGroup: {
          where: { deletedAt: null },
        },
        commission: {
          where: { deletedAt: null },
        },
      },
    });
  }

  /**
   * Detach a commission from a charge.
   */
  async detachCommission(chargeId: string): Promise<Charge> {
    return this.update({
      where: { id: chargeId },
      data: { commissionId: null },
      include: {
        percentageOfCharge: true,
        taxGroup: {
          where: { deletedAt: null },
        },
        commission: {
          where: { deletedAt: null },
        },
      },
    });
  }
}
