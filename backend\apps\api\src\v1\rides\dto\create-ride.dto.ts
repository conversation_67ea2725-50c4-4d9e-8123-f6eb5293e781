import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsArray,
  ValidateNested,
  IsOptional,
  IsNumber,
  Min,
  Max,
  IsEmail,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum BookFor {
  ME = 'me',
  OTHER = 'other',
}

export class RiderMetaDto {
  @ApiProperty({
    example: '<PERSON>',
    description: 'Full name of the rider',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    example: '+919876543210',
    description: 'Mobile number of the rider (required when booking for others)',
  })
  @IsNotEmpty()
  @IsString()
  phoneNumber!: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address of the rider',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  email?: string;
}

export class LocationDto {
  @ApiProperty({
    example: 12.9716,
    description: 'Latitude coordinate',
    minimum: -90,
    maximum: 90,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(-90)
  @Max(90)
  lat!: number;

  @ApiProperty({
    example: 77.5946,
    description: 'Longitude coordinate',
    minimum: -180,
    maximum: 180,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(-180)
  @Max(180)
  lng!: number;

  @ApiProperty({
    example: '123 Main Street, Bangalore, Karnataka, India',
    description: 'Address of the location',
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;
}

export class CreateRideDto {
  @ApiProperty({
    type: LocationDto,
    description: 'Pickup location coordinates',
    example: { lat: 12.9716, lng: 77.5946 },
  })
  @ValidateNested()
  @Type(() => LocationDto)
  pickup!: LocationDto;

  @ApiProperty({
    type: LocationDto,
    description: 'Destination location coordinates',
    example: { lat: 12.9352, lng: 77.6245 },
  })
  @ValidateNested()
  @Type(() => LocationDto)
  destination!: LocationDto;

  @ApiProperty({
    type: [LocationDto],
    description: 'Optional stops along the route',
    required: false,
    example: [
      { lat: 12.95, lng: 77.6 },
      { lat: 12.94, lng: 77.61 },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LocationDto)
  stops?: LocationDto[];

  @ApiProperty({
    example: 'product-uuid-123',
    description: 'Product ID for the ride service',
  })
  @IsNotEmpty()
  @IsString()
  productId!: string;

  @ApiProperty({
    enum: BookFor,
    example: BookFor.ME,
    description: 'Who is this ride for - me or other',
    default: BookFor.ME,
    required: false,
  })
  @IsOptional()
  @IsEnum(BookFor)
  bookFor?: BookFor = BookFor.ME;

  @ApiProperty({
    type: RiderMetaDto,
    description: 'Rider metadata when booking for others (required when bookFor=other)',
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => RiderMetaDto)
  riderMeta?: RiderMetaDto;
}
