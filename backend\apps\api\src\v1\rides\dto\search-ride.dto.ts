import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsEnum,
  IsOptional,
  IsDateString,
  ValidateNested,
  Min,
  Max,
  IsArray,
  IsString,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum RideSearchType {
  NOW = 'now',
  LATER = 'later',
}

export class LocationDto {
  @ApiProperty({
    example: 12.9716,
    description: 'Latitude coordinate',
    minimum: -90,
    maximum: 90,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(-90)
  @Max(90)
  lat!: number;

  @ApiProperty({
    example: 77.5946,
    description: 'Longitude coordinate',
    minimum: -180,
    maximum: 180,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(-180)
  @Max(180)
  lng!: number;

  @ApiProperty({
    example: '123 Main Street, Bangalore, Karnataka, India',
    description: 'Address of the location',
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;
}

export class SearchRideDto {
  @ApiProperty({
    type: LocationDto,
    description: 'Pickup location coordinates',
  })
  @ValidateNested()
  @Type(() => LocationDto)
  pickup!: LocationDto;

  @ApiProperty({
    type: LocationDto,
    description: 'Destination location coordinates',
  })
  @ValidateNested()
  @Type(() => LocationDto)
  destination!: LocationDto;

  @ApiProperty({
    enum: RideSearchType,
    example: RideSearchType.NOW,
    description: 'When to book the ride',
    default: RideSearchType.NOW,
    required: false,
  })
  @IsOptional()
  @IsEnum(RideSearchType)
  type?: RideSearchType = RideSearchType.NOW;

  @ApiProperty({
    example: '2024-12-25T14:30:00.000Z',
    description: 'Pickup time (required when type=later)',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  pickupTime?: string;

  //stops?: LocationDto[]; // Future enhancement for multiple stops
  @ApiProperty({
    type: [LocationDto],
    description: 'Optional stops along the route',
    required: false,
    example: [
      { lat: 12.95, lng: 77.6 },
      { lat: 12.94, lng: 77.61 },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LocationDto)
  stops?: LocationDto[];
}
