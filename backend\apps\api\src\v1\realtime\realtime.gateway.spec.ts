import { Test, TestingModule } from '@nestjs/testing';
import { RealtimeGateway } from './realtime.gateway';
import { RealtimeService } from './realtime.service';
import { RealtimeConnectionStateService } from './realtime-connection-state.service';
import { WsTokenManagerService } from '@shared/shared';
import { Server, Socket } from 'socket.io';

describe('RealtimeGateway', () => {
  let gateway: RealtimeGateway;
  let connectionState: jest.Mocked<RealtimeConnectionStateService>;
  let realtimeService: jest.Mocked<RealtimeService>;
  let wsTokenManager: jest.Mocked<WsTokenManagerService>;
  let mockServer: jest.Mocked<Server>;
  let mockSocket: jest.Mocked<Socket>;

  beforeEach(async () => {
    // Mock services
    const mockConnectionState = {
      addDriverConnection: jest.fn(),
      addRiderConnection: jest.fn(),
      removeDriverConnection: jest.fn(),
      removeRiderConnection: jest.fn(),
      cleanupSocketData: jest.fn(),
      isDriverConnected: jest.fn(),
      isRiderConnected: jest.fn(),
      getStatistics: jest.fn(),
      addRideSubscription: jest.fn(),
      removeRideSubscription: jest.fn(),
      getRideSubscribers: jest.fn(),
      setDriverRideMapping: jest.fn(),
      getDriverCurrentRide: jest.fn(),
      isHealthy: jest.fn(),
    };

    const mockRealtimeService = {
      extractUserDetailsWithDbLookup: jest.fn(),
      processLocationUpdate: jest.fn(),
    };

    const mockWsTokenManager = {
      authenticateSocket: jest.fn(),
      handleSocketDisconnect: jest.fn(),
      refreshSocketToken: jest.fn(),
    };

    // Redis adapter is now configured at application level, not in gateway

    // Mock Socket.IO server and socket
    mockServer = {
      adapter: jest.fn(),
      to: jest.fn().mockReturnThis(),
      emit: jest.fn(),
      sockets: {
        sockets: new Map(),
      },
    } as any;

    mockSocket = {
      id: 'test-socket-id',
      data: {},
      join: jest.fn(),
      leave: jest.fn(),
      emit: jest.fn(),
      disconnect: jest.fn(),
      handshake: {
        auth: {},
        headers: {},
        query: {},
      },
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RealtimeGateway,
        {
          provide: RealtimeService,
          useValue: mockRealtimeService,
        },
        {
          provide: WsTokenManagerService,
          useValue: mockWsTokenManager,
        },
        {
          provide: RealtimeConnectionStateService,
          useValue: mockConnectionState,
        },
      ],
    }).compile();

    gateway = module.get<RealtimeGateway>(RealtimeGateway);
    connectionState = module.get(RealtimeConnectionStateService);
    realtimeService = module.get(RealtimeService);
    wsTokenManager = module.get(WsTokenManagerService);
    // Set up the server
    gateway.server = mockServer;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Gateway Initialization', () => {
    it('should initialize gateway successfully', async () => {
      // Redis adapter is now configured at application level
      await gateway.afterInit(null);

      // No specific assertions needed since Redis is configured elsewhere
      // Just verify the method completes without error
      expect(true).toBe(true);
    });

    it('should log server information when initialized', async () => {
      const logSpy = jest.spyOn(gateway['logger'], 'log');

      await gateway.afterInit(null);

      expect(logSpy).toHaveBeenCalledWith('Realtime gateway initialized');
      expect(logSpy).toHaveBeenCalledWith(
        'Redis adapter configured at application level for horizontal scaling',
      );
    });
  });

  describe('Connection Management', () => {
    it('should handle driver connection successfully', async () => {
      const profileId = 'driver-123';
      const userType = 'driver';

      wsTokenManager.authenticateSocket.mockResolvedValue(true);
      realtimeService.extractUserDetailsWithDbLookup.mockResolvedValue({
        profileId,
        userType,
      });
      connectionState.addDriverConnection.mockResolvedValue();

      await gateway.handleConnection(mockSocket);

      expect(connectionState.addDriverConnection).toHaveBeenCalledWith(
        profileId,
        mockSocket.id,
      );
      expect(mockSocket.join).toHaveBeenCalledWith(`driver:${profileId}`);
      expect(mockSocket.emit).toHaveBeenCalledWith(
        'connected',
        expect.objectContaining({
          success: true,
          profileId,
          userType,
        }),
      );
    });

    it('should handle rider connection successfully', async () => {
      const profileId = 'rider-123';
      const userType = 'rider';

      wsTokenManager.authenticateSocket.mockResolvedValue(true);
      realtimeService.extractUserDetailsWithDbLookup.mockResolvedValue({
        profileId,
        userType,
      });
      connectionState.addRiderConnection.mockResolvedValue();

      await gateway.handleConnection(mockSocket);

      expect(connectionState.addRiderConnection).toHaveBeenCalledWith(
        profileId,
        mockSocket.id,
      );
      expect(mockSocket.join).toHaveBeenCalledWith(`rider:${profileId}`);
      expect(mockSocket.emit).toHaveBeenCalledWith(
        'connected',
        expect.objectContaining({
          success: true,
          profileId,
          userType,
        }),
      );
    });

    it('should handle authentication failure', async () => {
      mockSocket.handshake.auth['token'] = 'invalid-token';
      wsTokenManager.authenticateSocket.mockResolvedValue(false);

      await gateway.handleConnection(mockSocket);

      expect(mockSocket.disconnect).toHaveBeenCalledWith(true);
    });

    it('should handle disconnection successfully', async () => {
      mockSocket.data = { profileId: 'driver-123', userType: 'driver' };
      connectionState.cleanupSocketData.mockResolvedValue();

      await gateway.handleDisconnect(mockSocket);

      expect(wsTokenManager.handleSocketDisconnect).toHaveBeenCalledWith(
        mockSocket,
      );
      expect(connectionState.cleanupSocketData).toHaveBeenCalledWith(
        mockSocket.id,
      );
    });
  });

  describe('Ride Subscriptions', () => {
    it('should handle ride subscription successfully', async () => {
      const rideId = 'ride-123';
      const user = { profileId: 'rider-123' };
      (mockSocket as any).user = user;

      connectionState.addRideSubscription.mockResolvedValue();

      await gateway.handleRideSubscription({ rideId }, mockSocket);

      expect(connectionState.addRideSubscription).toHaveBeenCalledWith(
        rideId,
        mockSocket.id,
      );
      expect(mockSocket.join).toHaveBeenCalledWith(`ride:${rideId}`);
      expect(mockSocket.emit).toHaveBeenCalledWith(
        'subscription_success',
        expect.objectContaining({
          success: true,
          rideId,
        }),
      );
    });

    it('should handle ride unsubscription successfully', async () => {
      const rideId = 'ride-123';
      connectionState.removeRideSubscription.mockResolvedValue();

      await gateway.handleRideUnsubscription({ rideId }, mockSocket);

      expect(connectionState.removeRideSubscription).toHaveBeenCalledWith(
        rideId,
        mockSocket.id,
      );
      expect(mockSocket.leave).toHaveBeenCalledWith(`ride:${rideId}`);
      expect(mockSocket.emit).toHaveBeenCalledWith(
        'unsubscription_success',
        expect.objectContaining({
          success: true,
          rideId,
        }),
      );
    });
  });

  describe('Broadcasting Methods', () => {
    it('should broadcast ride status update', async () => {
      const update = {
        rideId: 'ride-123',
        status: 'driver_enroute' as any, // Use valid RideStatus enum value
        message: 'Driver is on the way',
        timestamp: new Date().toISOString(),
      };

      await gateway.broadcastRideStatusUpdate(update);

      expect(mockServer.to).toHaveBeenCalledWith(`ride:${update.rideId}`);
      expect(mockServer.emit).toHaveBeenCalledWith(
        'ride_status_updated',
        expect.objectContaining({
          ...update,
          timestamp: expect.any(String),
        }),
      );
    });

    it('should notify driver of ride offer', async () => {
      const driverId = 'driver-123';
      const offer = { offerId: 'offer-456', rideId: 'ride-789' };

      connectionState.isDriverConnected.mockResolvedValue(true);

      await gateway.notifyDriverOfRideOffer(driverId, offer);

      expect(connectionState.isDriverConnected).toHaveBeenCalledWith(driverId);
      expect(mockServer.to).toHaveBeenCalledWith(`driver:${driverId}`);
      expect(mockServer.emit).toHaveBeenCalledWith(
        'ride_offer_received',
        expect.objectContaining({
          ...offer,
          timestamp: expect.any(String),
        }),
      );
    });

    it('should skip notification if driver is not connected', async () => {
      const driverId = 'driver-123';
      const offer = { offerId: 'offer-456', rideId: 'ride-789' };

      connectionState.isDriverConnected.mockResolvedValue(false);

      await gateway.notifyDriverOfRideOffer(driverId, offer);

      expect(connectionState.isDriverConnected).toHaveBeenCalledWith(driverId);
      expect(mockServer.to).not.toHaveBeenCalled();
    });

    it('should broadcast driver location to ride subscribers', async () => {
      const rideId = 'ride-123';
      const locationData = {
        driverId: 'driver-456',
        lat: 40.7128,
        lon: -74.006, // Use 'lon' instead of 'lng' to match DriverLocationDto
        timestamp: new Date().toISOString(),
        status: 'online' as any, // Add required status field
        heading: 90,
        speed: 25,
      };

      connectionState.getRideSubscribers.mockResolvedValue([
        'socket-1',
        'socket-2',
      ]);

      await gateway.broadcastDriverLocationToRide(rideId, locationData);

      expect(connectionState.getRideSubscribers).toHaveBeenCalledWith(rideId);
      expect(mockServer.to).toHaveBeenCalledWith(`ride:${rideId}`);
      expect(mockServer.emit).toHaveBeenCalledWith(
        'driver_location_updated',
        expect.objectContaining({
          locationData,
          timestamp: expect.any(String),
          subscriberCount: 2,
        }),
      );
    });
  });

  describe('Statistics and Health', () => {
    it('should get statistics successfully', async () => {
      const mockStats = {
        connectedDrivers: 5,
        connectedRiders: 10,
        totalConnections: 15,
        activeRideSubscriptions: 3,
      };

      connectionState.getStatistics.mockResolvedValue(mockStats);

      const result = await gateway.getStatistics();

      expect(result).toEqual({
        ...mockStats,
        connectedSockets: 0,
      });
    });

    it('should perform health check successfully', async () => {
      connectionState.isHealthy.mockResolvedValue(true);

      const result = await gateway.isHealthy();

      expect(result).toBe(true);
      expect(connectionState.isHealthy).toHaveBeenCalled();
    });

    it('should handle health check failure', async () => {
      connectionState.isHealthy.mockResolvedValue(false);

      const result = await gateway.isHealthy();

      expect(result).toBe(false);
    });
  });
});
