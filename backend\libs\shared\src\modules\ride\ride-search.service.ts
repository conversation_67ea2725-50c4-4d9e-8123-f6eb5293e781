import {
  Injectable,
  BadRequestException,
  Logger,
  forwardRef,
  Inject,
} from '@nestjs/common';
import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';
import { H3IndexToZoneRepository } from '@shared/shared/repositories/h3IndexToZone.repository';
import { LocationIngestorService } from '../location/location-ingestor.service';
import {
  GoogleRouteMatrixService,
  Location,
} from '@shared/shared/common/google/google-route-matrix.service';
import { DriverMetadata } from '@shared/shared/repositories/models/redis/driverLocation.model';
import { ProductService } from '../product/product.service';
import { Product } from '@shared/shared/repositories/models/product.model';
import { ProductService as ProductServiceModel } from '@shared/shared/repositories/models/productService.model';
import { VehicleType } from '@shared/shared/repositories/models/vehicleType.model';
import { DriverCityProductRepository } from '@shared/shared/repositories/driver-city-product.repository';

// Enums for better type safety
export enum ServiceType {
  INTERCITY = 'intercity',
  CITY_RENTAL = 'city_rental',
  CITY_RIDE = 'city_ride',
}

export enum RideBookingType {
  NOW = 'now',
  LATER = 'later',
}

// Type alias for backward compatibility with controller
export type RideSearchType = RideBookingType;

// Input/Output interfaces
export interface SearchRideParams {
  pickup: {
    lat: number;
    lng: number;
  };
  destination: {
    lat: number;
    lng: number;
  };
  type?: RideBookingType | undefined;
  pickupTime?: string | undefined;
}

export interface RideSearchResult {
  id: string;
  name: string;
  identifier: string;
  serviceName: string;
  description?: string | undefined;
  icon?: string | undefined; // Full S3 URL
  price: number; // Dummy/hardcoded for now
  strikethroughPrice: number; // Default value
  passengerLimit: number; // Default value 2
  pickupToDestinationResult: {
    durationInSeconds: number; // Default 0
    distanceMeters: number; // Default 0
  };
  driverToPickupResults: {
    durationInSeconds: number; // Default 0
    distanceMeters: number; // Default 0
    estimatedArrivalTime: string | null; // now() + seconds(driverToPickupResults.durationInSeconds), default null if no driver data
  };
  driverToDestinationResults: {
    durationInSeconds: number; // pickupToDestinationResult.durationInSeconds + driverToPickupResults.durationInSeconds
    distanceMeters: number; // pickupToDestinationResult.distanceMeters + driverToPickupResults.distanceMeters
    estimatedArrivalTime: string | null; // now() + seconds(driverToDestinationResults.durationInSeconds)
  };
}

// Internal service types
export interface ProductWithRelations extends Product {
  productService?: ProductServiceModel;
  vehicleType?: VehicleType;
}

export interface NearbyDriver {
  driverId: string;
  metadata: DriverMetadata | null;
}

export interface DriverRouteData {
  driverId: string;
  etaToPickupSeconds: number;
  distanceToPickupMeters: number;
  estimatedArrivalTime: string;
  productId?: string | undefined; // Optional, can be undefined if not available
}

export interface RouteComputationResult {
  distanceMeters?: number | undefined;
  duration?:
    | {
        seconds: number;
      }
    | undefined;
}

export interface ZoneDetectionResult {
  serviceTypes: ServiceType[];
  cityIds?: string[] | undefined;
}

export interface ProductDriverMatch {
  product: ProductWithRelations;
  driverData?: DriverRouteData | undefined;
}

@Injectable()
export class RideSearchService {
  private readonly logger = new Logger(RideSearchService.name);
  private readonly H3_RESOLUTION = 8; // Resolution 8 for zone matching
  // private readonly DEFAULT_DRIVER_LIMIT = 10;
  private readonly DEFAULT_PRICE = 100; // Dummy price for now

  constructor(
    private readonly h3UtilityService: H3UtilityService,
    private readonly h3IndexToZoneRepository: H3IndexToZoneRepository,
    private readonly locationIngestorService: LocationIngestorService,
    private readonly googleRouteMatrixService: GoogleRouteMatrixService,
    @Inject(forwardRef(() => ProductService))
    private readonly productService: ProductService,
    private readonly driverCityProductRepository: DriverCityProductRepository,
  ) {}

  /**
   * Main method to search for available rides
   */
  async searchRides(params: SearchRideParams): Promise<RideSearchResult[]> {
    this.logger.log('Starting ride search...');

    // Step 1: Validate input parameters
    this.validatePickupTime(params);

    // Step 2: Determine service types based on pickup and destination zones
    const zoneResult = await this.determineServiceTypes(params);

    // Step 3: Get available products for the determined service types
    const products = await this.getProductsForServiceTypes(
      zoneResult.serviceTypes,
      zoneResult.cityIds,
    );

    if (products.length === 0) {
      this.logger.log('No enabled products found for the service types');
      return [];
    }

    // Step 4: Compute pickup to destination route
    const pickupToDestinationResult =
      await this.computePickupToDestinationRoute(params);
    this.logger.log(
      `Computed route from pickup to destination: ${JSON.stringify(pickupToDestinationResult)}`,
    );

    // Step 5: Handle "later" bookings (return products without driver matching)
    if (params.type === RideBookingType.LATER) {
      return this.mapProductsToRideSearchResults(
        products,
        pickupToDestinationResult,
      );
    }

    // Step 6: Find nearby drivers and compute routes for "now" bookings
    const driverRouteData = await this.findNearbyDriversWithRoutes(params);
    this.logger.log(`Found ${driverRouteData.length} nearby drivers`);
    // Step 7: If no drivers found, return products without driver data
    if (driverRouteData.length === 0) {
      this.logger.log('No drivers with valid location data found');
      return this.mapProductsToRideSearchResults(
        products,
        pickupToDestinationResult,
      );
    }

    // Step 8: Match products with best available drivers
    const productDriverMatches = await this.getBestDriverForProducts(
      products,
      driverRouteData,
    );

    // Step 9: Build and return results
    const results = this.buildRideSearchResults(
      productDriverMatches,
      pickupToDestinationResult,
    );

    this.logger.log(`Returning ${results.length} ride options`);
    return results;
  }

  /**
   * Validates pickup time for later bookings
   */
  private validatePickupTime(params: SearchRideParams): void {
    if (params.type === RideBookingType.LATER) {
      if (!params.pickupTime) {
        throw new BadRequestException(
          'Pickup time is required when type is later',
        );
      }

      const pickupDate = new Date(params.pickupTime);
      const now = new Date();

      if (pickupDate <= now) {
        throw new BadRequestException('Pickup time must be in the future');
      }
    }
  }

  /**
   * Determines service types based on pickup and destination zones
   */
  private async determineServiceTypes(
    params: SearchRideParams,
  ): Promise<ZoneDetectionResult> {
    // Convert coordinates to H3 indexes
    const pickupH3Index = this.h3UtilityService.coordinatesToH3Index(
      params.pickup.lat,
      params.pickup.lng,
      this.H3_RESOLUTION,
    );
    const destinationH3Index = this.h3UtilityService.coordinatesToH3Index(
      params.destination.lat,
      params.destination.lng,
      this.H3_RESOLUTION,
    );

    this.logger.log(`Pickup H3 index: ${pickupH3Index}`);
    this.logger.log(`Destination H3 index: ${destinationH3Index}`);

    // Check if pickup is in a city zone
    const pickupH3BigInt = this.h3UtilityService.stringToH3Index(pickupH3Index);
    const pickupCityZone =
      await this.h3IndexToZoneRepository.findFirstCityZonesByH3Index(
        pickupH3BigInt,
      );

    let serviceTypes: ServiceType[] = [ServiceType.INTERCITY]; // Default to intercity
    let cityIds: string[] = [];
    // console.log(pickupCityZone);

    if (pickupCityZone && pickupCityZone.zone?.cityId) {
      const pickupCityId = pickupCityZone.zone?.cityId;
      // cityIds.push(pickupCityZone.zone?.cityId);
      // console.log(pickupCityZone);
      this.logger.log(`Pickup is in city zone`);

      // Check if destination is also in a city zone
      const destinationH3BigInt =
        this.h3UtilityService.stringToH3Index(destinationH3Index);
      const destinationCityZone =
        await this.h3IndexToZoneRepository.findFirstCityZonesByH3Index(
          destinationH3BigInt,
        );

      if (
        destinationCityZone &&
        destinationCityZone.zone?.cityId &&
        destinationCityZone.zone?.cityId === pickupCityId
      ) {
        this.logger.log(`Destination is in same city zone`);
        this.logger.log('Processing city and rental services');
        serviceTypes = [ServiceType.CITY_RENTAL, ServiceType.CITY_RIDE];
        cityIds.push(pickupCityId);
      } else {
        this.logger.log('Destination is not in a city zone - intercity rides');
        serviceTypes = [ServiceType.INTERCITY];
      }
    } else {
      this.logger.log('Pickup is not in a city zone - intercity rides');
      serviceTypes = [ServiceType.INTERCITY];
    }
    this.logger.log(`Determined service types: ${serviceTypes.join(', ')}`);
    this.logger.log(`Determined city IDs: ${cityIds.join(', ')}`);
    return { serviceTypes, cityIds };
  }

  /**
   * Fetches enabled products for the given service types
   */
  private async getProductsForServiceTypes(
    serviceTypes: ServiceType[],
    cityIds?: string[],
  ): Promise<ProductWithRelations[]> {
    const serviceIdentifiers = serviceTypes.map((type) => type.toString());
    this.logger.log(
      `Fetching products for service types: ${serviceIdentifiers.join(', ')}`,
    );
    console.log(
      `Fetching products for service types: ${serviceIdentifiers.join(', ')}`,
    );
    // console.log(`City IDs: ${cityIds ? cityIds.join(', ') : 'None'}`);
    return await this.productService.findEnabledProductsByServiceIdentifiers(
      // serviceIdentifiers,
      cityIds,
    );
  }

  /**
   * Computes route from pickup to destination
   */
  private async computePickupToDestinationRoute(
    params: SearchRideParams,
  ): Promise<RouteComputationResult | null> {
    return await this.googleRouteMatrixService.computePickupToDestination(
      { lat: params.pickup.lat, lng: params.pickup.lng },
      { lat: params.destination.lat, lng: params.destination.lng },
    );
  }

  /**
   * Maps a single product to RideSearchResult
   */
  private mapProductToRideSearchResult(
    product: ProductWithRelations,
    pickupToDestinationResult: RouteComputationResult | null,
    driverData?: DriverRouteData,
  ): RideSearchResult {
    // Extract pickup to destination data
    const pickupToDestinationDurationSeconds =
      pickupToDestinationResult?.duration
        ? this.parseDuration(pickupToDestinationResult.duration.toString())
        : 0;
    const pickupToDestinationDistanceMeters =
      pickupToDestinationResult?.distanceMeters || 0;

    // Extract driver to pickup data
    const driverToPickupDurationSeconds = driverData?.etaToPickupSeconds || 0;
    const driverToPickupDistanceMeters =
      driverData?.distanceToPickupMeters || 0;
    const driverToPickupArrivalTime =
      driverData && driverToPickupDurationSeconds > 0
        ? new Date(
            Date.now() + driverToPickupDurationSeconds * 1000,
          ).toISOString()
        : null;

    // Calculate driver to destination data
    const driverToDestinationDurationSeconds =
      pickupToDestinationDurationSeconds + driverToPickupDurationSeconds;
    const driverToDestinationDistanceMeters =
      pickupToDestinationDistanceMeters + driverToPickupDistanceMeters;
    const driverToDestinationArrivalTime =
      driverToDestinationDurationSeconds > 0
        ? new Date(
            Date.now() + driverToDestinationDurationSeconds * 1000,
          ).toISOString()
        : null;

    return {
      id: product.id,
      identifier: product.identifier ?? '',
      name: product.name,
      serviceName: product.productService?.name || 'Unknown Service',
      description: product.description || undefined,
      icon: product.icon || undefined,
      price: this.DEFAULT_PRICE,
      strikethroughPrice: this.DEFAULT_PRICE + 50, // Default strikethrough price
      passengerLimit: product.passengerLimit ?? 0,
      pickupToDestinationResult: {
        durationInSeconds: pickupToDestinationDurationSeconds,
        distanceMeters: pickupToDestinationDistanceMeters,
      },
      driverToPickupResults: {
        durationInSeconds: driverToPickupDurationSeconds,
        distanceMeters: driverToPickupDistanceMeters,
        estimatedArrivalTime: driverToPickupArrivalTime,
      },
      driverToDestinationResults: {
        durationInSeconds: driverToDestinationDurationSeconds,
        distanceMeters: driverToDestinationDistanceMeters,
        estimatedArrivalTime: driverToDestinationArrivalTime,
      },
    };
  }

  /**
   * Maps multiple products to RideSearchResults
   */
  private mapProductsToRideSearchResults(
    products: ProductWithRelations[],
    pickupToDestinationResult: RouteComputationResult | null,
  ): RideSearchResult[] {
    this.logger.log(
      `Mapping ${products.length} products to ride search results`,
    );

    return products.map((product) =>
      this.mapProductToRideSearchResult(product, pickupToDestinationResult),
    );
  }

  /**
   * Finds nearby drivers and computes route data
   */
  private async findNearbyDriversWithRoutes(
    params: SearchRideParams,
  ): Promise<DriverRouteData[]> {
    const pickupH3Index = this.h3UtilityService.coordinatesToH3Index(
      params.pickup.lat,
      params.pickup.lng,
      this.H3_RESOLUTION,
    );
    this.logger.log(`Pickup H3 index for driver search: ${pickupH3Index}`);

    this.logger.log('Finding nearby drivers...');

    const nearbyDrivers: NearbyDriver[] =
      await this.locationIngestorService.findAllNearbyDrivers(
        pickupH3Index,
        2,
        50,
      );

    if (nearbyDrivers.length === 0) {
      this.logger.log('No drivers found in the area');
      return [];
    }

    this.logger.log(`Found ${nearbyDrivers.length} nearby drivers`);

    // Log driver details for debugging
    nearbyDrivers.forEach((driver, index) => {
      this.logger.log(
        `Driver ${index + 1}: ID=${driver.driverId}, hasMetadata=${!!driver.metadata}, lat=${driver.metadata?.lat}, lon=${driver.metadata?.lng}, productId=${driver.metadata?.productId}`,
      );
    });

    // Filter drivers with valid location data
    const validDrivers = nearbyDrivers.filter(
      (driver: NearbyDriver) => driver.metadata?.lat && driver.metadata?.lng,
    );

    this.logger.log(
      `Filtered to ${validDrivers.length} drivers with valid location data`,
    );

    if (validDrivers.length === 0) {
      this.logger.log('No drivers with valid location data found');
      return [];
    }

    // Get driver locations for route matrix API
    const driverLocations: Location[] = validDrivers.map(
      (driver: NearbyDriver) => ({
        lat: driver.metadata!.lat,
        lng: driver.metadata!.lng,
      }),
    );

    // Compute routes from drivers to pickup location
    this.logger.log('Computing route matrices for drivers to pickup...');

    const driversToPickupResults =
      await this.googleRouteMatrixService.computeDriversToPickup(
        driverLocations,
        { lat: params.pickup.lat, lng: params.pickup.lng },
      );

    this.logger.log(
      `Computed route matrices for ${driversToPickupResults.length} drivers`,
    );

    const driverRouteData: DriverRouteData[] = validDrivers.map(
      (driver: NearbyDriver, index: number) => {
        const routeResult = driversToPickupResults[index];

        const etaToPickupSeconds = this.parseDuration(
          (routeResult?.duration || '0').toString(),
        );

        const distanceToPickupMeters = routeResult?.distanceMeters || 0;
        const estimatedArrivalTime =
          etaToPickupSeconds > 0
            ? new Date(Date.now() + etaToPickupSeconds * 1000)
            : new Date();

        return {
          driverId: driver.driverId,
          etaToPickupSeconds,
          distanceToPickupMeters,
          estimatedArrivalTime: estimatedArrivalTime.toISOString(),
          productId: driver.metadata?.productId,
        };
      },
    );

    this.logger.log(
      `Computed route data for ${driverRouteData.length} drivers`,
    );

    console.log({
      message: 'driverRouteData',
      data: driverRouteData,
    });

    return driverRouteData;
  }

  /**
   * Matches products with the best available drivers using database lookup for driver-product associations
   * Uses findDriversForProducts to get accurate driver-product mappings since drivers can have multiple products
   */
  private async getBestDriverForProducts(
    products: ProductWithRelations[],
    driverRouteData: DriverRouteData[],
  ): Promise<ProductDriverMatch[]> {
    const productDriverMatches: ProductDriverMatch[] = [];

    this.logger.log(
      `Matching ${products.length} products with ${driverRouteData.length} drivers using database lookup`,
    );

    // Extract driver IDs and product IDs for database lookup
    const driverIds = driverRouteData.map((driver) => driver.driverId);
    const productIds = products.map((product) => product.id);

    this.logger.log(
      `Looking up driver-product associations for ${driverIds.length} drivers and ${productIds.length} products`,
    );
    //TODO: need to optimise this query
    // Get driver-product associations from database
    const driverProductAssociations = await this.getDriverProductAssociations(
      driverIds,
      productIds,
    );
    console.log({
      message: 'driverProductAssociations',
      data: driverProductAssociations,
    });
    // Create a map for quick lookup: productId -> eligible drivers
    const productToDriversMap = new Map<string, DriverRouteData[]>();

    // Initialize map with empty arrays for all products
    products.forEach((product) => {
      productToDriversMap.set(product.id, []);
    });

    // Populate the map based on database associations
    driverProductAssociations.forEach(
      (association: { userProfileId: string; productId: string }) => {
        const driverData = driverRouteData.find(
          (driver) => driver.driverId === association.userProfileId,
        );
        if (driverData) {
          const eligibleDrivers =
            productToDriversMap.get(association.productId) || [];
          eligibleDrivers.push(driverData);
          productToDriversMap.set(association.productId, eligibleDrivers);
        }
      },
    );

    // Log all available products and their eligible drivers
    products.forEach((product, index) => {
      const eligibleDrivers = productToDriversMap.get(product.id) || [];
      this.logger.log(
        `Product ${index + 1}: ID=${product.id}, name=${product.name}, eligible drivers: ${eligibleDrivers.length}`,
      );
    });

    // For each product, find the best driver who can serve it
    for (const product of products) {
      const eligibleDriverRouteData = productToDriversMap.get(product.id) || [];

      this.logger.log(
        `Product ${product.name} (ID: ${product.id}): Found ${eligibleDriverRouteData.length} eligible drivers`,
      );

      if (eligibleDriverRouteData.length === 0) {
        // No drivers can serve this product
        this.logger.log(
          `No eligible drivers found for product: ${product.name}`,
        );
        productDriverMatches.push({
          product,
          driverData: undefined,
        });
        continue;
      }

      // Find the driver with the shortest ETA among eligible drivers
      const bestDriverForProduct = eligibleDriverRouteData.sort(
        (a, b) => a.etaToPickupSeconds - b.etaToPickupSeconds,
      )[0];

      this.logger.log(
        `Best driver for product ${product.name}: ${bestDriverForProduct.driverId} (ETA: ${bestDriverForProduct.etaToPickupSeconds} sec)`,
      );

      productDriverMatches.push({
        product,
        driverData: bestDriverForProduct,
      });
    }

    return productDriverMatches;
  }

  /**
   * Builds the final ride search results
   */
  private buildRideSearchResults(
    productDriverMatches: ProductDriverMatch[],
    pickupToDestinationResult: RouteComputationResult | null,
  ): RideSearchResult[] {
    return productDriverMatches.map((match) =>
      this.mapProductToRideSearchResult(
        match.product,
        pickupToDestinationResult,
        match.driverData,
      ),
    );
  }

  private parseDuration(duration: string): number {
    if (!duration) return 0;

    // Remove trailing 's' if it exists and convert to number
    return Number(duration.endsWith('s') ? duration.slice(0, -1) : duration);
  }

  /**
   * Get driver-product associations from database using findDriversForProducts
   */
  private async getDriverProductAssociations(
    driverIds: string[],
    productIds: string[],
  ): Promise<{ userProfileId: string; productId: string }[]> {
    if (driverIds.length === 0 || productIds.length === 0) {
      return [];
    }

    try {
      return await this.driverCityProductRepository.findDriversForProducts(
        driverIds,
        productIds,
      );
    } catch (error) {
      this.logger.error('Failed to get driver-product associations:', error);
      return [];
    }
  }
}
