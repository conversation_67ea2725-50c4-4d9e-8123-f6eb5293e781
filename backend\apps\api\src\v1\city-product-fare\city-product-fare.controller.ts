import {
  Controller,
  Get,
  Post,
  Patch,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CityProductFareService } from '@shared/shared/modules/city-product-fare/city-product-fare.service';
import { CityProductFareStatus } from '@shared/shared/repositories/models/cityProductFare.model';
import { CreateCityProductFareDto } from './dto/create-city-product-fare.dto';
import { AttachChargeGroupsToFareDto } from './dto/attach-charge-groups-to-fare.dto';
import { UpdateChargeGroupPrioritiesDto } from './dto/update-charge-group-priorities.dto';
import { FareSelectionQueryDto } from './dto/fare-selection-query.dto';
import {
  CityProductFareResponseDto,
  AttachChargeGroupsResponseDto,
  DetachChargeGroupResponseDto,
  UpdatePrioritiesResponseDto,
  FareSelectionResponseDto,
} from './dto/city-product-fare-response.dto';
import {
  ApiResponseDto,
  ApiErrorResponseDto,
  PaginatedResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('City Product Fares')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('city-product-fares')
export class CityProductFareController {
  constructor(
    private readonly cityProductFareService: CityProductFareService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new city product fare rule',
    description:
      'Create a new fare rule for a city product with optional zone restrictions and priority',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'City product fare created successfully',
    type: ApiResponseDto<CityProductFareResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'City product or zones not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Fare rule already exists for this city product and zones',
    type: ApiErrorResponseDto,
  })
  async create(@Body() createDto: CreateCityProductFareDto) {
    const data = await this.cityProductFareService.createCityProductFare({
      cityProductId: createDto.cityProductId,
      priority: createDto.priority,
      status: createDto.status || CityProductFareStatus.ACTIVE,
      fromZoneId: createDto.fromZoneId || null,
      toZoneId: createDto.toZoneId || null,
      fromZoneTypeId: createDto.fromZoneTypeId || null,
      toZoneTypeId: createDto.toZoneTypeId || null,
    });
    return {
      success: true,
      message: 'City product fare created successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get(':cityProductId')
  @ApiOperation({
    summary: 'Get all fare rules for a city product',
    description:
      'Retrieve all fare rules associated with a specific city product',
  })
  @ApiParam({
    name: 'cityProductId',
    description: 'City product ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Fare rules retrieved successfully',
    type: ApiResponseDto<CityProductFareResponseDto[]>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'City product not found',
    type: ApiErrorResponseDto,
  })
  async findByCityProductId(@Param('cityProductId') cityProductId: string) {
    const data =
      await this.cityProductFareService.findCityProductFaresByCityProductId(
        cityProductId,
      );
    return {
      success: true,
      message: 'Fare rules retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Post(':fareId/charge-groups')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Attach charge groups to a fare rule',
    description:
      'Attach multiple charge groups to a city product fare rule with priority ordering',
  })
  @ApiParam({
    name: 'fareId',
    description: 'City product fare ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Charge groups attached successfully',
    type: ApiResponseDto<AttachChargeGroupsResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or duplicate priorities',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Fare rule or charge groups not found',
    type: ApiErrorResponseDto,
  })
  async attachChargeGroups(
    @Param('fareId', ParseUUIDPipe) fareId: string,
    @Body() dto: AttachChargeGroupsToFareDto,
  ) {
    // Fetch the highest priority from existing charge groups for this fare
    const existingGroups =
      await this.cityProductFareService.getFareChargeGroups(fareId);
    const maxPriority = existingGroups.length
      ? Math.max(...existingGroups.map((cg: any) => cg.priority ?? 0))
      : 0;

    // Assign priorities: if not provided, set to maxPriority + index + 1
    const chargeGroupsWithPriority = dto.chargeGroups.map((cg, idx) => ({
      chargeGroupId: cg.chargeGroupId,
      priority: cg.priority ?? maxPriority + idx + 1,
    }));

    const data = await this.cityProductFareService.attachChargeGroupsToFare(
      fareId,
      chargeGroupsWithPriority,
    );

    return {
      success: true,
      message: 'Charge groups attached successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':fareId/charge-groups/:chargeGroupId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Detach a charge group from a fare rule',
    description: 'Remove a charge group from a city product fare rule',
  })
  @ApiParam({
    name: 'fareId',
    description: 'City product fare ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiParam({
    name: 'chargeGroupId',
    description: 'Charge group ID to detach',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charge group detached successfully',
    type: ApiResponseDto<DetachChargeGroupResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Fare rule or charge group not found',
    type: ApiErrorResponseDto,
  })
  async detachChargeGroup(
    @Param('fareId', ParseUUIDPipe) fareId: string,
    @Param('chargeGroupId', ParseUUIDPipe) chargeGroupId: string,
  ) {
    await this.cityProductFareService.detachChargeGroupFromFare(
      fareId,
      chargeGroupId,
    );

    return {
      success: true,
      message: 'Charge group detached successfully',
      data: { message: 'Charge group detached successfully' },
      timestamp: Date.now(),
    };
  }

  @Put(':fareId/charge-groups/priorities')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update charge group priorities for a fare rule',
    description:
      'Update the priority order of charge groups within a city product fare rule',
  })
  @ApiParam({
    name: 'fareId',
    description: 'City product fare ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charge group priorities updated successfully',
    type: ApiResponseDto<UpdatePrioritiesResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or duplicate priorities',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Fare rule or charge groups not found',
    type: ApiErrorResponseDto,
  })
  async updateChargeGroupPriorities(
    @Param('fareId', ParseUUIDPipe) fareId: string,
    @Body() dto: UpdateChargeGroupPrioritiesDto,
  ) {
    const data = await this.cityProductFareService.updateChargeGroupPriorities(
      fareId,
      dto.chargeGroups.map((cg) => ({
        chargeGroupId: cg.chargeGroupId,
        priority: cg.priority,
      })),
    );

    return {
      success: true,
      message: 'Charge group priorities updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('select')
  @ApiOperation({
    summary: 'Select best fare rule based on zones',
    description:
      'Find the best matching fare rule based on pickup and destination zones using priority-based selection',
  })
  @ApiQuery({
    name: 'pickupZoneId',
    required: false,
    description: 'Pickup zone ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiQuery({
    name: 'destinationZoneId',
    required: false,
    description: 'Destination zone ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Best fare rule selected successfully',
    type: ApiResponseDto<FareSelectionResponseDto>,
  })
  async selectFare(@Query() query: FareSelectionQueryDto) {
    const data = await this.cityProductFareService.selectBestFareRule(
      query.pickupZoneId,
      query.destinationZoneId,
    );

    return {
      success: true,
      message: data
        ? 'Fare rule selected successfully'
        : 'No matching fare rule found for the specified zones',
      data,
      timestamp: Date.now(),
    };
  }

  @Get(':fareId/charge-groups')
  @ApiOperation({
    summary: 'Get charge groups for a fare rule',
    description:
      'Retrieve all charge groups attached to a specific city product fare rule',
  })
  @ApiParam({
    name: 'fareId',
    description: 'City product fare ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charge groups retrieved successfully',
    type: ApiResponseDto<AttachChargeGroupsResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Fare rule not found',
    type: ApiErrorResponseDto,
  })
  async getFareChargeGroups(@Param('fareId', ParseUUIDPipe) fareId: string) {
    const data = await this.cityProductFareService.getFareChargeGroups(fareId);

    return {
      success: true,
      message: 'Charge groups retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Patch(':fareId')
  @ApiOperation({
    summary: 'Update city product fare rule',
    description: 'Update an existing city product fare rule',
  })
  @ApiParam({
    name: 'fareId',
    description: 'City product fare ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Fare rule updated successfully',
    type: ApiResponseDto<CityProductFareResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Fare rule not found',
    type: ApiErrorResponseDto,
  })
  async update(
    @Param('fareId', ParseUUIDPipe) fareId: string,
    @Body() updateDto: Partial<CreateCityProductFareDto>,
  ) {
    const data = await this.cityProductFareService.updateCityProductFare(
      fareId,
      updateDto,
    );

    return {
      success: true,
      message: 'Fare rule updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':fareId')
  @ApiOperation({
    summary: 'Delete city product fare rule (soft delete)',
    description: 'Soft delete a city product fare rule',
  })
  @ApiParam({
    name: 'fareId',
    description: 'City product fare ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Fare rule deleted successfully',
    type: ApiResponseDto<CityProductFareResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Fare rule not found',
    type: ApiErrorResponseDto,
  })
  async delete(@Param('fareId', ParseUUIDPipe) fareId: string) {
    const data =
      await this.cityProductFareService.deleteCityProductFare(fareId);

    return {
      success: true,
      message: 'Fare rule deleted successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get()
  @ApiOperation({
    summary: 'Get paginated city product fares',
    description:
      'Retrieve paginated list of city product fares with optional filtering',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'cityProductId',
    required: false,
    description: 'Filter by city product ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Fare rules retrieved successfully',
    type: PaginatedResponseDto<CityProductFareResponseDto>,
  })
  async paginate(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('cityProductId') cityProductId?: string,
  ) {
    const options: any = {};
    if (cityProductId) {
      options.where = { cityProductId };
    }

    const result = await this.cityProductFareService.paginateCityProductFares(
      Number(page),
      Number(limit),
      options,
    );

    return {
      success: true,
      message: 'Fare rules retrieved successfully',
      data: result.data,
      meta: {
        page: result.meta.page,
        limit: result.meta.limit,
        total: result.meta.total,
        totalPages: result.meta.totalPages,
        hasNextPage: result.meta.hasNextPage,
        hasPreviousPage: result.meta.hasPrevPage,
      },
      timestamp: Date.now(),
    };
  }
}
