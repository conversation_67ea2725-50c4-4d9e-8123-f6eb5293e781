-- CreateEnum
CREATE TYPE "commission_type" AS ENUM ('percentage', 'flat');

-- AlterTable
ALTER TABLE "charges" ADD COLUMN     "isCommon" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "commissions" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" "commission_type" NOT NULL,
    "percentage_value" DECIMAL(5,2),
    "flat_value" DECIMAL(10,2),
    "tax_group_id" UUID,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "commissions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_commission_name" ON "commissions"("name");

-- CreateIndex
CREATE INDEX "idx_commission_type" ON "commissions"("type");

-- CreateIndex
CREATE INDEX "idx_commission_tax_group_id" ON "commissions"("tax_group_id");

-- CreateIndex
CREATE UNIQUE INDEX "commissions_name_key" ON "commissions"("name");

-- AddForeignKey
ALTER TABLE "commissions" ADD CONSTRAINT "commissions_tax_group_id_fkey" FOREIGN KEY ("tax_group_id") REFERENCES "tax_groups"("id") ON DELETE SET NULL ON UPDATE CASCADE;
