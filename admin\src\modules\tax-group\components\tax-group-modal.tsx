'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { Textarea } from '@/components/ui/textarea';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus, Trash2 } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useCreateTaxGroup, useUpdateTaxGroup } from '../api/mutations';
import { useGetTaxGroup } from '../api/queries';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';

const taxSubcategorySchema = z.object({
   name: z
      .string()
      .min(1, 'Subcategory name is required')
      .min(2, 'Subcategory name must be at least 2 characters')
      .max(100, 'Subcategory name must not exceed 100 characters'),
   percentage: z
      .number()
      .min(0, 'Percentage must be at least 0')
      .max(100, 'Percentage must not exceed 100'),
});

const taxGroupSchema = z.object({
   name: z
      .string()
      .min(1, 'Tax group name is required')
      .min(2, 'Tax group name must be at least 2 characters')
      .max(100, 'Tax group name must not exceed 100 characters'),
   description: z.string().max(500, 'Description must not exceed 500 characters').optional(),
   subcategories: z
      .array(taxSubcategorySchema)
      .min(1, 'At least one subcategory is required')
      .max(10, 'Maximum 10 subcategories allowed'),
});

type TaxGroupFormValues = z.infer<typeof taxGroupSchema>;

interface TaxGroupModalProps {
   taxGroupId?: string | null;
   isOpen?: boolean;
   onClose?: () => void;
   mode?: 'create' | 'edit';
}

export const TaxGroupModal = ({
   taxGroupId,
   isOpen,
   onClose,
   mode = 'create',
}: TaxGroupModalProps) => {
   const [internalOpen, setInternalOpen] = useState(false);
   const createTaxGroupMutation = useCreateTaxGroup();
   const updateTaxGroupMutation = useUpdateTaxGroup();
   const taxGroupQuery = useGetTaxGroup(taxGroupId || null);
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   // Use external open state if provided, otherwise use internal state
   const modalOpen = isOpen !== undefined ? isOpen : internalOpen;
   const setModalOpen = onClose ? (open: boolean) => !open && onClose() : setInternalOpen;

   const form = useForm<TaxGroupFormValues>({
      resolver: zodResolver(taxGroupSchema),
      defaultValues: {
         name: '',
         description: '',
         subcategories: [{ name: '', percentage: 0 }],
      },
   });

   const {
      formState: { errors },
      reset,
      control,
      handleSubmit,
   } = form;

   const { fields, append, remove } = useFieldArray({
      control,
      name: 'subcategories',
   });

   const isDataLoading = taxGroupQuery.isLoading;

   // Reset form when taxGroupId changes or modal opens
   useEffect(() => {
      if (mode === 'edit' && taxGroupQuery.data?.data && modalOpen && !isDataLoading) {
         const taxGroup = taxGroupQuery.data.data;
         reset({
            name: taxGroup.name,
            description: taxGroup.description || '',
            subcategories:
               taxGroup.subcategories.length > 0
                  ? taxGroup.subcategories.map(sub => ({
                       name: sub.name,
                       percentage: parseFloat(sub.percentage),
                    }))
                  : [{ name: '', percentage: 0 }],
         });
      } else if (mode === 'create') {
         reset({
            name: '',
            description: '',
            subcategories: [{ name: '', percentage: 0 }],
         });
      }
   }, [taxGroupQuery.data, reset, mode, modalOpen, isDataLoading]);

   const onSubmit = async (data: TaxGroupFormValues) => {
      try {
         const payload = {
            name: data.name,
            description: data.description || undefined,
            subcategories: data.subcategories,
         };

         if (mode === 'create') {
            createTaxGroupMutation.mutate(payload, {
               onSuccess: () => {
                  toast.success('Tax group created successfully');
                  handleClose();
                  queryClient.invalidateQueries({ queryKey: ['tax-groups'] });
               },
            });
         } else if (mode === 'edit' && taxGroupId) {
            updateTaxGroupMutation.mutate(
               { id: taxGroupId, ...payload },
               {
                  onSuccess: () => {
                     toast.success('Tax group updated successfully');
                     handleClose();
                     queryClient.invalidateQueries({ queryKey: ['tax-groups'] });
                     queryClient.invalidateQueries({
                        queryKey: ['tax-group', taxGroupId],
                     });
                  },
               }
            );
         }
      } catch (error: any) {
         console.error('Submit error:', error);
      }
   };

   const handleClose = () => {
      setModalOpen(false);
      reset();
   };

   const isLoading =
      mode === 'create' ? createTaxGroupMutation.isPending : updateTaxGroupMutation.isPending;

   // Show loading state for edit mode
   if (mode === 'edit' && taxGroupQuery.isLoading) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Loading...</DialogTitle>
                  <DialogDescription>
                     Please wait while we load the tax group data.
                  </DialogDescription>
               </DialogHeader>
               <div className='flex items-center justify-center py-8'>
                  <Spinner className='h-8 w-8' />
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   // Show error state for edit mode
   if (mode === 'edit' && taxGroupQuery.error) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Error</DialogTitle>
                  <DialogDescription>Failed to load tax group data.</DialogDescription>
               </DialogHeader>
               <div className='text-center py-8'>
                  <p className='text-red-600'>Failed to load tax group data</p>
                  <Button onClick={handleClose} className='mt-4'>
                     Close
                  </Button>
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   const content = (
      <DialogContent
         onInteractOutside={e => {
            e.preventDefault();
         }}
         className='max-w-2xl max-h-[90vh] overflow-y-auto'
      >
         <DialogHeader>
            <DialogTitle>
               {mode === 'create' ? 'Create New Tax Group' : 'Edit Tax Group'}
            </DialogTitle>
            <DialogDescription>
               {mode === 'create'
                  ? 'Add a new tax group with subcategories to the system'
                  : 'Update the tax group information and subcategories'}
            </DialogDescription>
         </DialogHeader>

         <form onSubmit={handleSubmit(onSubmit)} className='space-y-4 py-4'>
            <div className='flex flex-col gap-2'>
               <Label htmlFor='name'>Tax Group Name *</Label>
               <Controller
                  control={control}
                  name='name'
                  render={({ field }) => (
                     <Input
                        id='name'
                        placeholder='e.g. GST, VAT, Service Tax'
                        {...field}
                        className='w-full'
                     />
                  )}
               />
               {errors.name && <ErrorMessage error={errors.name} />}
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='description'>Description</Label>
               <Controller
                  control={control}
                  name='description'
                  render={({ field }) => (
                     <Textarea
                        id='description'
                        placeholder='Enter tax group description'
                        {...field}
                        className='w-full min-h-[80px] resize-none'
                     />
                  )}
               />
               {errors.description && <ErrorMessage error={errors.description} />}
            </div>

            <div className='flex flex-col gap-3'>
               <div className='flex items-center justify-between'>
                  <Label>Subcategories *</Label>
                  <Button
                     type='button'
                     size='sm'
                     variant='outline'
                     onClick={() => append({ name: '', percentage: 0 })}
                     disabled={fields.length >= 10}
                  >
                     <Plus className='h-4 w-4 mr-1' />
                     Add Subcategory
                  </Button>
               </div>

               <div className='space-y-3'>
                  {fields.map((field, index) => (
                     <div key={field.id} className='flex gap-2 items-start'>
                        <div className='flex-1 flex flex-col gap-2'>
                           <Label htmlFor={`subcategories.${index}.name`}>Name</Label>
                           <Controller
                              control={control}
                              name={`subcategories.${index}.name`}
                              render={({ field }) => (
                                 <Input
                                    {...field}
                                    id={`subcategories.${index}.name`}
                                    placeholder='e.g. CGST, SGST'
                                    className='w-full'
                                 />
                              )}
                           />
                           {errors.subcategories?.[index]?.name && (
                              <ErrorMessage error={errors.subcategories[index]!.name!} />
                           )}
                        </div>

                        <div className='w-28 flex flex-col gap-2'>
                           <Label htmlFor={`subcategories.${index}.percentage`}>Percentage</Label>
                           <Controller
                              control={control}
                              name={`subcategories.${index}.percentage`}
                              render={({ field }) => (
                                 <Input
                                    {...field}
                                    id={`subcategories.${index}.percentage`}
                                    type='number'
                                    step='0.01'
                                    min='0'
                                    max='100'
                                    placeholder='%'
                                    onChange={e =>
                                       field.onChange(parseFloat(e.target.value) || 0)
                                    }
                                    className='w-full'
                                 />
                              )}
                           />
                           {errors.subcategories?.[index]?.percentage && (
                              <ErrorMessage error={errors.subcategories[index]!.percentage!} />
                           )}
                        </div>

                        <Button
                           type='button'
                           size='icon'
                           variant='ghost'
                           onClick={() => remove(index)}
                           disabled={fields.length === 1}
                           className='mt-7'
                        >
                           <Trash2 className='h-4 w-4 text-red-600' />
                        </Button>
                     </div>
                  ))}
               </div>

               {errors.subcategories && typeof errors.subcategories.message === 'string' && (
                  <ErrorMessage error={{ message: errors.subcategories.message }} />
               )}
            </div>

            <div className='flex gap-3 pt-4'>
               <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                  Cancel
               </Button>
               <Button type='submit' disabled={isLoading} className='flex-1'>
                  {isLoading ? (
                     <>
                        {mode === 'create' ? 'Creating...' : 'Updating...'}
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : mode === 'create' ? (
                     'Create Tax Group'
                  ) : (
                     'Update Tax Group'
                  )}
               </Button>
            </div>
         </form>
      </DialogContent>
   );

   // For create mode, return button and dialog separately
   if (mode === 'create' && isOpen === undefined) {
      return (
         <>
            <Button
               className='cursor-pointer'
               variant='outline'
               onClick={() =>
                  withPermission(RBAC_PERMISSIONS.TAX_GROUP.CREATE, () => setModalOpen(true))
               }
            >
               <Plus />
               Add Tax Group
            </Button>
            <Dialog open={modalOpen} onOpenChange={setModalOpen}>
               {content}
            </Dialog>
         </>
      );
   }

   // For edit mode or controlled create mode
   return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
         {content}
      </Dialog>
   );
};
