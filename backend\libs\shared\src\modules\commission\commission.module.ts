import { Module } from '@nestjs/common';
import { CommissionService } from './commission.service';
import { CommissionRepository } from '../../repositories/commission.repository';
import { TaxGroupRepository } from '../../repositories/tax-group.repository';
import { RepositoryModule } from '../../repositories/repository.module';

@Module({
  imports: [RepositoryModule],
  providers: [
    CommissionService,
    CommissionRepository,
    TaxGroupRepository,
  ],
  exports: [CommissionService],
})
export class CommissionModule {}
